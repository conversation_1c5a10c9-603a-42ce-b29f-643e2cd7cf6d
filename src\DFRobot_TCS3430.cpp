/*!
 * @file DFRobot_TCS3430.cpp
 * @brief Implementation of DFRobot_TCS3430 class
 * @copyright Copyright (c) 2010 DFRobot Co.Ltd (http://www.dfrobot.com)
 * @SKU SEN0403
 * @licence The MIT License (MIT)
 * <AUTHOR>
 * @version V1.0
 * @date 2021-01-26
 * @url https://github.com/DFRobot/DFRobot_TCS3400
 */
#include "DFRobot_TCS3430.h"

DFRobot_TCS3430::DFRobot_TCS3430(TwoWire *pWire): _pWire(pWire),_deviceAddr(DFRobot_TCS3430_ICC_ADDR) {
  _atime = 0;
  _wtime = 0;
  _wlong = 0;
  _enableReg.pon = 0;
  _enableReg.aen = 0;
  _enableReg.reservedBit2 = 0;
  _enableReg.wen = 0;
  _enableReg.reservedBit4_7 = 0;
  _cfg1Reg.again = 0;
  _cfg1Reg.reservedBit2 = 0;
  _cfg1Reg.amux = 0;
  _cfg1Reg.reservedBit4_7 = 0;
  _cfg3Reg.reservedBit0_3 = 0;
  _cfg3Reg.sai = 0;
  _cfg3Reg.reservedBit5_6 = 0;
  _cfg3Reg.intReadClear = 0;
  _AZCfgReg.azNTHIteration = 0x7F;
  _AZCfgReg.azMode = 0;
  _intEnabReg.reservedBit0_3 = 0;
  _intEnabReg.aien = 0;
  _intEnabReg.reservedBit5_6 =0;
  _intEnabReg.asien = 0;
  
  // Initialize calibration data
  _calibration.whiteX = 0;
  _calibration.whiteY = 0;
  _calibration.whiteZ = 0;
  _calibration.darkX = 0;
  _calibration.darkY = 0;
  _calibration.darkZ = 0;
  _calibration.isCalibrated = false;
}

bool DFRobot_TCS3430::begin() {
  _pWire->begin();
  softReset();
  setPowerALSADC();
  if((getDeviceID()!=TCS3430_ID) || (getRevisionID() != TCS3430_REVISION_ID)){
    disableALSADC();
    powerOFF();
    return false;
  }
  return true;
}

void DFRobot_TCS3430::setIntegrationTime(uint8_t aTime) {
  _atime = aTime;
  write(eRegATIMEAddr,aTime);
}

void DFRobot_TCS3430::setWaitTime(uint8_t wTime) {
  _wtime = wTime;
  write(eRegWTIMEAddr,wTime);
}

void DFRobot_TCS3430:: setPowerALSADC() {
  _enableReg.pon = 1;
  _enableReg.aen = 1;
  write(eRegENABLEAddr,*((uint8_t*)(&_enableReg)));
}

void DFRobot_TCS3430:: powerOFF() {
  _enableReg.pon = 0;
  write(eRegENABLEAddr,*((uint8_t*)(&_enableReg)));
}

void DFRobot_TCS3430:: disableALSADC() {
  _enableReg.aen = 0;
  write(eRegENABLEAddr,*((uint8_t*)(&_enableReg)));
}

void DFRobot_TCS3430:: setWaitTimer(bool mode) {
  if(mode){
    _enableReg.wen = 1;
    write(eRegENABLEAddr,*((uint8_t*)(&_enableReg)));
  } else{
    _enableReg.wen = 0;
    write(eRegENABLEAddr,*((uint8_t*)(&_enableReg)));
  }
}

void DFRobot_TCS3430:: setInterruptPersistence(uint8_t apers) {
  write(eRegPERSAddr,apers);
}

void DFRobot_TCS3430:: setWaitLong(bool mode) {
  if(mode){
    write(eRegCFG0Addr,DFRobot_TCS3430_CONFIG_WLONG);
    _wlong =1;
  }else{
    write(eRegCFG0Addr,DFRobot_TCS3430_CONFIG_NO_WLONG);
    _wlong =0;
  }
}

void DFRobot_TCS3430:: setALSGain(uint8_t aGain) {
  _cfg1Reg.again=aGain;
  write(eRegCFG1Addr,*((uint8_t*)(&_cfg1Reg)));
}

void DFRobot_TCS3430:: setIR2Channel(bool mode) {
  if(mode){
    _cfg1Reg.amux=1;
    write(eRegCFG1Addr,*((uint8_t*)(&_cfg1Reg)));
  }else{
    _cfg1Reg.amux=0;
    write(eRegCFG1Addr,*((uint8_t*)(&_cfg1Reg)));
  }
}

uint8_t DFRobot_TCS3430:: getRevisionID() {
  return uint8_t(read(eRegREVIDAddr,1));
}

uint8_t DFRobot_TCS3430:: getDeviceID() {
  return uint8_t(read(eRegIDAddr,1));
}

uint8_t DFRobot_TCS3430:: getDeviceStatus() {
  return uint8_t(read(eRegSTATUSAddr,1));
}

uint16_t DFRobot_TCS3430:: getZData() {
  return read(eRegCH0DATALAddr,TWO_BYTE);
}

uint16_t DFRobot_TCS3430:: getYData() {
  return DFRobot_TCS3430:: read(eRegCH1DATALAddr,TWO_BYTE);
}

uint16_t DFRobot_TCS3430:: getIR1Data() {
  return read(eRegCH2DATALAddr,TWO_BYTE);
}

uint16_t DFRobot_TCS3430:: getXData() {
  return read(eRegCH3DATALAddr,TWO_BYTE);
}

uint16_t DFRobot_TCS3430:: getIR2Data() {
  disableALSADC();
  setIR2Channel(true);
  _enableReg.aen = 1;
  write(eRegENABLEAddr,*((uint8_t*)(&_enableReg)));
  uint16_t delayTime = 0;
  if(_wlong){
    delayTime = (_atime+1)*2.78 + (_wtime+1)*33.4;
  }else{
    delayTime = (_atime+1)*2.78 + (_wtime+1)*2.78;
  }
  delay(delayTime);
  uint16_t value = read(eRegCH3DATALAddr,TWO_BYTE);
  disableALSADC();
  setIR2Channel(false);
  _enableReg.aen = 1;
  write(eRegENABLEAddr,*((uint8_t*)(&_enableReg)));
  delay(delayTime);
  return value;
}

void DFRobot_TCS3430:: setHighGAIN(bool mode) {
  if (mode){
    write(eRegCFG2Addr,DFRobot_TCS3430_HGAIN_ENABLE);
  }else{
    write(eRegCFG2Addr,DFRobot_TCS3430_HGAIN_DISABLE);
  }
}

void DFRobot_TCS3430:: setIntReadClear(bool mode) {
  if (mode){
    _cfg3Reg.intReadClear = 1;
    write(eRegCFG3Addr,*((uint8_t*)(&_cfg3Reg)));
  }else{
    _cfg3Reg.intReadClear = 0;
    write(eRegCFG3Addr,*((uint8_t*)(&_cfg3Reg)));
  }
}

void DFRobot_TCS3430:: setSleepAfterInterrupt(bool mode ) {
  if (mode){
    _cfg3Reg.sai = 1;
    write(eRegCFG3Addr,*((uint8_t*)(&_cfg3Reg)));
  }else{
    _cfg3Reg.sai = 0;
    write(eRegCFG3Addr,*((uint8_t*)(&_cfg3Reg)));
  }
}

void DFRobot_TCS3430:: setAutoZeroMode(uint8_t mode) {
  if(mode == 0){
    _AZCfgReg.azMode = 0;
  }
  if(mode == 1){
    _AZCfgReg.azMode = 1;
  }
  write(eRegAZCONFIGAddr,*((uint8_t*)(&_AZCfgReg)));
}

void DFRobot_TCS3430:: setAutoZeroNTHIteration(uint8_t value) {
  _AZCfgReg.azNTHIteration = value &0x7F;
  write(eRegAZCONFIGAddr,*((uint8_t*)(&_AZCfgReg)));
}

void DFRobot_TCS3430:: setALSSaturationInterrupt(bool mode ) {
  setIntReadClear(true);
  if(mode){
    _intEnabReg.asien = 1;
    write(eRegINTENABAddr,*((uint8_t*)(&_intEnabReg)));
  }else{
    _intEnabReg.asien = 0;
    write(eRegINTENABAddr,*((uint8_t*)(&_intEnabReg)));
  }
}

void DFRobot_TCS3430:: setALSInterrupt(bool mode ) {
  setIntReadClear(true);
  if(mode){
    _intEnabReg.aien = 1;
    write(eRegINTENABAddr,*((uint8_t*)(&_intEnabReg)));
  }else{
    _intEnabReg.aien = 0;
    write(eRegINTENABAddr,*((uint8_t*)(&_intEnabReg)));
  }
}

void DFRobot_TCS3430:: setCH0IntThreshold(uint16_t thresholdL,uint16_t thresholdH) {
  uint8_t ailtl = thresholdL&0x00FF;
  uint8_t ailth = (thresholdL&0xFF00)>>8;
  uint8_t aihtl = thresholdH&0x00FF;
  uint8_t aihth = (thresholdH&0xFF00)>>8;
  write(eRegAILTLAddr,ailtl);
  write(eRegAILTHAddr,ailth);
  write(eRegAIHTLAddr,aihtl);
  write(eRegAIHTHAddr,aihth);
}

void DFRobot_TCS3430:: softReset() {
  setWaitTimer(false);
  setIntegrationTime(0x23);  // ~100ms integration time for better accuracy
  setWaitTime(0);
  setWaitLong(false);
  setALSGain(1);  // Start with 4x gain instead of 64x for better white detection
  setHighGAIN(false);
  setIntReadClear(false);
  setSleepAfterInterrupt(false);
  setAutoZeroMode(0);
  setAutoZeroNTHIteration(0x7f);
  setALSInterrupt(false);
  setALSSaturationInterrupt(false);
}

void DFRobot_TCS3430:: write(uint8_t regAddr,uint8_t value) {
  _pWire->beginTransmission(_deviceAddr);
  _pWire->write(regAddr);
  _pWire->write(value);
  _pWire->endTransmission();
}

uint16_t DFRobot_TCS3430:: read(uint8_t regAddr,uint8_t readNum) {
  uint16_t value=0;
  _pWire->beginTransmission(_deviceAddr);
  _pWire->write(regAddr);
  _pWire->endTransmission();
  _pWire->requestFrom(_deviceAddr, readNum);
  if(readNum==1){
    value = _pWire->read();
  }else if(readNum == 2){
    value = _pWire->read();
    value |= _pWire->read()<<8;
  }
  return value;
}

// Enhanced calibration and color conversion functions
bool DFRobot_TCS3430::performWhiteBalanceCalibration(int numSamples) {
  Serial.println("Starting white balance calibration...");
  Serial.println("Place a white reference (white paper) under the sensor and ensure good lighting");

  // Use optimal settings for white balance
  setALSGain(1);  // 4x gain
  setIntegrationTime(0x23);  // ~100ms integration time
  delay(200);  // Allow sensor to stabilize

  float totalX = 0, totalY = 0, totalZ = 0;
  int validSamples = 0;

  for (int i = 0; i < numSamples; i++) {
    delay(120);  // Wait for integration time + margin

    uint16_t x = getXData();
    uint16_t y = getYData();
    uint16_t z = getZData();

    // Check for saturation
    if (x < 65000 && y < 65000 && z < 65000 && x > 100 && y > 100 && z > 100) {
      totalX += x;
      totalY += y;
      totalZ += z;
      validSamples++;
      Serial.printf("White sample %d: X=%d, Y=%d, Z=%d\n", i+1, x, y, z);
    } else {
      Serial.printf("White sample %d: INVALID (saturated or too low)\n", i+1);
    }
  }

  if (validSamples >= numSamples/2) {
    _calibration.whiteX = totalX / validSamples;
    _calibration.whiteY = totalY / validSamples;
    _calibration.whiteZ = totalZ / validSamples;

    Serial.printf("White balance calibration successful!\n");
    Serial.printf("White reference: X=%.1f, Y=%.1f, Z=%.1f\n",
                  _calibration.whiteX, _calibration.whiteY, _calibration.whiteZ);

    // Enable calibration with just white balance (dark calibration is optional)
    _calibration.isCalibrated = true;
    return true;
  } else {
    Serial.println("White balance calibration failed - insufficient valid samples");
    return false;
  }
}

bool DFRobot_TCS3430::performDarkCalibration(int numSamples) {
  Serial.println("Starting dark calibration...");
  Serial.println("Cover the sensor completely (block all light) for dark calibration");

  delay(2000);  // Give user time to cover sensor

  float totalX = 0, totalY = 0, totalZ = 0;

  for (int i = 0; i < numSamples; i++) {
    delay(120);  // Wait for integration time

    uint16_t x = getXData();
    uint16_t y = getYData();
    uint16_t z = getZData();

    totalX += x;
    totalY += y;
    totalZ += z;

    Serial.printf("Dark sample %d: X=%d, Y=%d, Z=%d\n", i+1, x, y, z);
  }

  _calibration.darkX = totalX / numSamples;
  _calibration.darkY = totalY / numSamples;
  _calibration.darkZ = totalZ / numSamples;

  Serial.printf("Dark calibration complete!\n");
  Serial.printf("Dark reference: X=%.1f, Y=%.1f, Z=%.1f\n",
                _calibration.darkX, _calibration.darkY, _calibration.darkZ);

  if (_calibration.whiteX != 0 || _calibration.whiteY != 0 || _calibration.whiteZ != 0) {
    _calibration.isCalibrated = true;
  }
  return true;
}

bool DFRobot_TCS3430::getCalibratedXYZ(float *x, float *y, float *z) {
  uint16_t rawX = getXData();
  uint16_t rawY = getYData();
  uint16_t rawZ = getZData();

  if (_calibration.isCalibrated) {
    // Check if we have dark calibration data
    bool hasDarkCalibration = (_calibration.darkX != 0 || _calibration.darkY != 0 || _calibration.darkZ != 0);

    if (hasDarkCalibration) {
      // Apply dark subtraction and white balance normalization
      *x = (rawX - _calibration.darkX) / (_calibration.whiteX - _calibration.darkX);
      *y = (rawY - _calibration.darkY) / (_calibration.whiteY - _calibration.darkY);
      *z = (rawZ - _calibration.darkZ) / (_calibration.whiteZ - _calibration.darkZ);
    } else {
      // Only white balance calibration available - normalize to white reference
      *x = rawX / _calibration.whiteX;
      *y = rawY / _calibration.whiteY;
      *z = rawZ / _calibration.whiteZ;
    }

    // Clamp to valid range
    *x = max(0.0f, min(1.0f, *x));
    *y = max(0.0f, min(1.0f, *y));
    *z = max(0.0f, min(1.0f, *z));

    return true;
  } else {
    // Return normalized raw values
    *x = rawX / 65535.0f;
    *y = rawY / 65535.0f;
    *z = rawZ / 65535.0f;
    return false;
  }
}

void DFRobot_TCS3430::convertXYZtoRGB(float x, float y, float z, uint8_t *r, uint8_t *g, uint8_t *b) {
  // XYZ to sRGB conversion matrix (D65 illuminant)
  // Based on CIE 1931 XYZ color space to sRGB conversion
  float R =  3.2406f * x - 1.5372f * y - 0.4986f * z;
  float G = -0.9689f * x + 1.8758f * y + 0.0415f * z;
  float B =  0.0557f * x - 0.2040f * y + 1.0570f * z;

  // Apply gamma correction (sRGB)
  auto gammaCorrect = [](float c) -> float {
    if (c <= 0.0031308f) {
      return 12.92f * c;
    } else {
      return 1.055f * pow(c, 1.0f/2.4f) - 0.055f;
    }
  };

  R = gammaCorrect(R);
  G = gammaCorrect(G);
  B = gammaCorrect(B);

  // Clamp and convert to 8-bit
  *r = (uint8_t)(max(0.0f, min(1.0f, R)) * 255);
  *g = (uint8_t)(max(0.0f, min(1.0f, G)) * 255);
  *b = (uint8_t)(max(0.0f, min(1.0f, B)) * 255);
}

uint8_t DFRobot_TCS3430::getOptimalGain() {
  // Read current values with current gain
  uint16_t x = getXData();
  uint16_t y = getYData();
  uint16_t z = getZData();

  uint16_t maxVal = max(x, max(y, z));

  // Determine optimal gain based on signal level
  if (maxVal > 50000) {
    return 0;  // 1x gain - signal too high
  } else if (maxVal > 12000) {
    return 1;  // 4x gain - good signal level
  } else if (maxVal > 3000) {
    return 2;  // 16x gain - moderate signal
  } else {
    return 3;  // 64x gain - low signal
  }
}

bool DFRobot_TCS3430::isSaturated() {
  uint16_t x = getXData();
  uint16_t y = getYData();
  uint16_t z = getZData();

  // Check if any channel is near saturation (> 95% of max value)
  return (x > 62000 || y > 62000 || z > 62000);
}

float DFRobot_TCS3430::getIntegrationTimeMs() {
  // Integration time = (ATIME + 1) * 2.78ms
  return (_atime + 1) * 2.78f;
}
