#include "utilities.h"
#include "config.h" // For RGB_LED_PIN

// If using Adafruit NeoPixel library for a WS2812 type LED:
// #include <Adafruit_NeoPixel.h>
// #define NUM_LEDS 1
// Adafruit_NeoPixel onboard_led(NUM_LEDS, RGB_LED_PIN, NEO_GRB + NEO_KHZ800);
bool rgb_led_initialized_flag = false;

void log_message(int level, const char* component_tag, const char* message) {
    if (level > CURRENT_LOG_LEVEL || !Serial) { // Don't log if level is too high or Serial not ready
        return;
    }

    const char* level_str;
    switch (level) {
        case LOG_LEVEL_ERROR: level_str = "E"; break; // Error
        case LOG_LEVEL_WARN:  level_str = "W"; break; // Warning
        case LOG_LEVEL_INFO:  level_str = "I"; break; // Info
        case LOG_LEVEL_DEBUG: level_str = "D"; break; // Debug
        default:              level_str = "V"; break; // Verbose / Unknown
    }
    // Timestamp (ms), Level, Component Tag, Message
    Serial.printf("%lu [%s] [%s]: %s\n", millis(), level_str, component_tag, message);
}


void init_rgb_led() {
    // Example for a simple GPIO-controlled single-color LED (active HIGH)
    // If RGB_LED_PIN is for a WS2812 (NeoPixel), replace with NeoPixel initialization:
    // onboard_led.begin();
    // onboard_led.show(); // Initialize all pixels to 'off'
    // onboard_led.setBrightness(50); // Set brightness (0-255)
    // rgb_led_initialized_flag = true;
    // log_info("LED", "RGB LED (NeoPixel) initialized.");
    
    // For a simple LED connected to a single GPIO pin:
    if (RGB_LED_PIN >= 0) { // Check if a valid pin is defined
        pinMode(RGB_LED_PIN, OUTPUT);
        digitalWrite(RGB_LED_PIN, LOW); // Start with LED off
        rgb_led_initialized_flag = true;
        log_info("LED", "Simple LED initialized on pin " STRINGIFY(RGB_LED_PIN) ".");
    } else {
        log_warning("LED", "RGB_LED_PIN not defined or invalid. LED not initialized.");
    }
}

void set_rgb_led_color(uint8_t r, uint8_t g, uint8_t b) {
    if (!rgb_led_initialized_flag || RGB_LED_PIN < 0) return;

    // Example for WS2812 (NeoPixel):
    // onboard_led.setPixelColor(0, onboard_led.Color(r, g, b));
    // onboard_led.show();

    // Example for a simple single-color LED connected to RGB_LED_PIN:
    // Turn LED ON if any color component is significantly bright (e.g., average > some threshold)
    // This is a very basic representation.
    if ((r + g + b) / 3 > 20) { // If average brightness is above a threshold
        digitalWrite(RGB_LED_PIN, HIGH); // Turn LED ON
    } else {
        digitalWrite(RGB_LED_PIN, LOW);  // Turn LED OFF
    }
}

// ESP32-S3-Touch-LCD-1.69 board-specific functions

void init_buzzer() {
    pinMode(BUZZER_PIN, OUTPUT);
    digitalWrite(BUZZER_PIN, LOW); // Start with buzzer off
    log_info("Buzzer", "Buzzer initialized on GPIO42.");
}

void buzzer_beep(uint16_t frequency, uint16_t duration_ms) {
    if (frequency == 0 || duration_ms == 0) {
        buzzer_off();
        return;
    }

    // Use tone() function for PWM-based buzzer control
    tone(BUZZER_PIN, frequency, duration_ms);

    // Create log message string
    String log_msg = "Beep: " + String(frequency) + "Hz for " + String(duration_ms) + "ms";
    log_debug("Buzzer", log_msg.c_str());
}

void buzzer_tone(uint16_t frequency) {
    if (frequency == 0) {
        buzzer_off();
        return;
    }

    tone(BUZZER_PIN, frequency);

    // Create log message string
    String log_msg = "Tone: " + String(frequency) + "Hz";
    log_debug("Buzzer", log_msg.c_str());
}

void buzzer_off() {
    noTone(BUZZER_PIN);
    digitalWrite(BUZZER_PIN, LOW);
}

// Power management functions

void init_power_control() {
    pinMode(SYS_EN_PIN, OUTPUT);
    pinMode(SYS_OUT_PIN, INPUT);

    // Enable system power by default
    digitalWrite(SYS_EN_PIN, HIGH);

    log_info("Power", "Power control initialized. SYS_EN=GPIO41, SYS_OUT=GPIO40");
}

void system_power_enable(bool enable) {
    digitalWrite(SYS_EN_PIN, enable ? HIGH : LOW);
    log_info("Power", enable ? "System power enabled" : "System power disabled");
}

bool get_system_power_status() {
    return digitalRead(SYS_OUT_PIN) == HIGH;
}