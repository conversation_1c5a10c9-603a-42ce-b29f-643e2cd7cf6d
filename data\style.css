body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif; background-color: #f4f7f6; color: #333; margin: 0; padding: 20px; }
.container { max-width: 800px; margin: auto; }
h1 { color: #1a237e; text-align: center; }
.card { background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); padding: 25px; margin-bottom: 20px; }
.color-display { display: flex; justify-content: space-around; align-items: flex-start; flex-wrap: wrap; gap: 20px; }
.color-card { flex: 1; min-width: 250px; text-align: center; }
.color-card h2 { margin-top: 0; color: #3949ab; border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; }
.swatch { width: 100%; height: 120px; border-radius: 6px; border: 1px solid #ccc; background-color: #f0f0f0; margin-bottom: 15px; transition: background-color 0.5s ease; }
.details p { margin: 8px 0; font-size: 1.1em; }
.stats { margin-top: 15px; }
.stats p { font-size: 1.2em; font-weight: bold; text-align: center; margin: 5px 0; }
#confidence.high { color: #2e7d32; }
#confidence.medium { color: #f57f17; }
#confidence.low { color: #c62828; }
.collapsible-header { cursor: pointer; padding: 12px; background-color: #e8eaf6; border-radius: 8px; font-weight: bold; color: #1a237e; margin-top: 10px; user-select: none; }
.collapsible-header::after { content: '\25BC'; float: right; transition: transform 0.2s; }
.collapsible-header.active::after { transform: rotate(180deg); }
.collapsible-content { padding: 0 15px; max-height: 0; overflow: hidden; transition: max-height 0.3s ease-out; background-color: #fafafa; border-radius: 0 0 8px 8px; }
.live-data-grid, .form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; padding: 15px 0; }
label { font-weight: bold; margin-bottom: 5px; display: block; }
input, select { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; }
.form-actions { display: flex; justify-content: flex-end; align-items: center; margin-top: 15px; gap: 15px; }
button { background-color: #3949ab; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 1em; }
button:hover { background-color: #1a237e; }
#save-status { text-align: right; color: green; font-weight: bold; height: 1em; }
.control-buttons { display: flex; gap: 10px; justify-content: center; margin-bottom: 20px; }
#led-button.on { background-color: #2e7d32; }
#led-button.off { background-color: #c62828; }
#scan-button.stop { background-color: #c62828; }
#white-balance-button { background-color: #f57c00; }
#white-balance-button:hover { background-color: #ef6c00; }
#black-calibration-button { background-color: #424242; }
#black-calibration-button:hover { background-color: #212121; }
#scan-status { text-align: center; color: #3949ab; font-weight: bold; margin-top: 10px; }
footer { margin-top: 20px; font-size: 0.9em; color: #777; text-align: center; }
.setting-description { font-size: 0.9em; color: #666; margin-top: 3px; margin-bottom: 10px; }
.setting-group { border-left: 3px solid #3949ab; padding-left: 15px; margin-bottom: 20px; }
.setting-group h3 { color: #3949ab; margin-top: 0; }
.preview-container { display: flex; gap: 20px; margin-top: 15px; }
.preview-box { flex: 1; text-align: center; }
.preview-swatch { width: 100%; height: 80px; border-radius: 6px; border: 1px solid #ccc; margin-bottom: 10px; }
.preview-data { font-size: 0.9em; }
.tabs { display: flex; margin-bottom: 15px; border-bottom: 1px solid #ddd; }
.tab { padding: 10px 20px; cursor: pointer; border-radius: 5px 5px 0 0; }
.tab.active { background-color: #e8eaf6; border: 1px solid #ddd; border-bottom: none; font-weight: bold; }
.tab-content { display: none; }
.tab-content.active { display: block; }
.recommended-values { background-color: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 10px; font-size: 0.9em; }
.recommended-values h4 { margin-top: 0; margin-bottom: 5px; color: #3949ab; }
.recommended-values ul { margin: 0; padding-left: 20px; }
.error-message { color: #c62828; font-size: 0.9em; margin-top: 5px; display: none; }
.nav-buttons { display: flex; justify-content: space-between; margin-top: 20px; }
.nav-buttons button { background-color: #757575; }
.nav-buttons button:hover { background-color: #616161; }

/* Quality indicators styling */
.quality-indicators { padding: 15px; }
.quality-item { display: flex; justify-content: space-between; margin-bottom: 8px; }
.quality-label { font-weight: bold; color: #555; }
.quality-value { font-weight: bold; }
.quality-value.excellent { color: #2e7d32; }
.quality-value.good { color: #388e3c; }
.quality-value.fair { color: #f57c00; }
.quality-value.poor { color: #c62828; }
.quality-value.calibrated { color: #2e7d32; }
.quality-value.not-calibrated { color: #c62828; }
.quality-value.saturated { color: #c62828; }
.quality-value.normal { color: #2e7d32; }
