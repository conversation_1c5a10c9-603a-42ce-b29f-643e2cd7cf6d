#ifndef COLOR_CONVERSION_H
#define COLOR_CONVERSION_H

#include <Arduino.h> // For constrain, fmax, etc.
#include <math.h>    // For powf, sqrtf

// Basic sRGB color structure (0-255 range)
typedef struct {
    uint8_t r;
    uint8_t g;
    uint8_t b;
} rgb_color_s;

// CIE L*a*b* color structure
typedef struct {
    float l; // Lightness (0-100)
    float a; // Green-Red axis
    float b; // Blue-Yellow axis
} lab_color_s;

// CIE XYZ tristimulus values structure
// Can represent raw sensor values or normalized/scaled values.
typedef struct {
    float x;
    float y;
    float z;
} xyz_color_s;

// Standard D65 White Point reference values (Y = 100)
extern const xyz_color_s D65_WHITEPOINT;

// Converts raw sensor XYZ values to an sRGB structure for display.
// `normalization_factor` is crucial: typically the sensor's Y-channel reading for a white reference.
rgb_color_s xyz_to_display_srgb(const xyz_color_s &xyz_sensor_values, float normalization_factor);

// Converts CIE XYZ values to CIE L*a*b* values.
// `xyz_in` should be scaled appropriately relative to `white_point_ref`.
// e.g., if `white_point_ref` is D65 (Y=100), then `xyz_in.y` should also be on a scale where 100 represents white.
void xyz_to_lab(const xyz_color_s &xyz_in, lab_color_s &lab_out, const xyz_color_s &white_point_ref);

// Converts sRGB (0-255) to CIE XYZ values, scaled such that Y is 100 for sRGB white (255,255,255) under D65.
xyz_color_s srgb_to_xyz(const rgb_color_s &srgb_color);

// Calculates Delta E (CIE76) color difference between two L*a*b* colors.
float delta_e_cie76(const lab_color_s &lab1, const lab_color_s &lab2);

// Applies a simple IR compensation to raw XYZ sensor data.
// `k_ir_comp_factor` needs empirical calibration.
void apply_ir_compensation(xyz_color_s &xyz_raw_data, uint16_t ir1_raw, uint16_t ir2_raw, float k_ir_comp_factor);

#endif // COLOR_CONVERSION_H