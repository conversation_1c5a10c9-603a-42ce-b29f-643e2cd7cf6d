// ====================================================================================
// ESP32PROS3 Color Matcher - Dulux Local Database Master Edition
//
// This definitive version uses a large local 'dulux.json' file for its color
// database, parsed efficiently from the ESP32's LittleFS filesystem. It hosts
// a full-featured web UI for real-time color matching and sensor calibration.
//
// Final Version By: Arduino Master Coder
// Architecture: Local Database (Offline Capable)
// Merged with features from old version for enhanced functionality.
//
// Key Features:
// - Loads over 1300+ colors from a local 'dulux.json' file.
// - Uses the standard ArduinoJson library with a streaming parser for memory efficiency.
// - Fully interactive web UI for real-time data and calibration.
// - Robust WiFi, OTA, and WebSocket logging implementation.
// - Fallback to a small, built-in color list if the local file is not found.
// - Restored white balance calibration, IR compensation, sensor health checks, and LED control from old version.
// - Interrupt handling for real-time sensor events.
//
// REQUIRES:
// 1. ESP32 Board Support Package (ESP32-S3).
// 2. DFRobot_TCS3430 library.
// 3. ESPAsyncWebServer, AsyncTCP, LittleFS, ArduinoOTA libraries.
// 4. ArduinoJson library (by Benoît Blanchon) - v7 compatible.
// 5. "ESP32 Sketch Data Uploader" tool.
// 6. A 'dulux.json' file present in the sketch's '/data' directory.
// ====================================================================================

// ------------------------------------------------------------------------------------
// Section 1: Configuration
// ------------------------------------------------------------------------------------
#define I2C_SDA_PIN          8
#define I2C_SCL_PIN          9
#define INDICATOR_LED_PIN    5
#define ILLUMINATION_LED_PIN 4
#define READING_INTERVAL_MS  500
#define SENSOR_INTERRUPT_PIN 21
#define LOG_LEVEL_ERROR      0
#define LOG_LEVEL_WARN       1
#define LOG_LEVEL_INFO       2
#define LOG_LEVEL_DEBUG      3
#define CURRENT_LOG_LEVEL    LOG_LEVEL_INFO
#define MAX_COLOR_NAME_LEN   35      // Increased for longer Dulux names
#define FORMAT_LITTLEFS_IF_FAILED true
#define DELTA_E_THRESHOLD    5.0
#define MAX_DULUX_COLORS     1500    // Increased for the large local database
#define WIFI_CHECK_INTERVAL  10000
#define OTA_REFRESH_INTERVAL 300000
#define WIFI_MAX_RETRIES     2
#define WIFI_RETRY_DELAY_MS  3000

// ------------------------------------------------------------------------------------
// Section 1.5: Network Configuration
// ------------------------------------------------------------------------------------
const char* SSID = "Wifi 6";
const char* PASSWORD = "Scrofani1985";
const IPAddress STATIC_IP(192, 168, 0, 201);
const IPAddress GATEWAY(192, 168, 0, 1);
const IPAddress SUBNET(255, 255, 255, 0);
const IPAddress DNS_SERVER_PRIMARY(8, 8, 8, 8);
const IPAddress DNS_SERVER_SECONDARY(8, 8, 4, 4);

// ------------------------------------------------------------------------------------
// Section 2: Library Includes
// ------------------------------------------------------------------------------------
#include <Wire.h>
#include <DFRobot_TCS3430.h>
#include <Preferences.h>
#include <math.h>
#include <nvs_flash.h>
#include <WiFi.h>
#include <ESPAsyncWebServer.h>
#include <AsyncTCP.h>
#include <ArduinoJson.h>
#include <LittleFS.h>
#include <ArduinoOTA.h>
#include <esp_task_wdt.h>
#include <esp_system.h>

// ------------------------------------------------------------------------------------
// Section 2.5: Global Type Definitions
// ------------------------------------------------------------------------------------
typedef struct { uint8_t r; uint8_t g; uint8_t b; } rgb_color_s;
typedef struct { float l; float a; float b; } lab_color_s;
typedef struct { float x; float y; float z; } xyz_color_s;

typedef struct {
    char name[MAX_COLOR_NAME_LEN];
    rgb_color_s rgb_ref;
    lab_color_s lab_ref;
    bool is_valid;
} reference_color_t;

typedef struct {
    uint8_t measured_r, measured_g, measured_b;
    char matched_name[MAX_COLOR_NAME_LEN];
    uint8_t matched_r, matched_g, matched_b;
    float delta_e;
    char confidence[10];
    float avg_x, avg_y, avg_z;
    float avg_l, avg_a, avg_b;
    float avg_ir1, avg_ir2;
    bool data_ready;
} web_ui_data_t;

typedef struct {
    float k_ir_compensation_factor;
    float srgb_output_norm_factor;
    uint8_t als_gain;
    uint8_t integration_time;
    bool adaptive_scaling;
    bool data_is_valid;
} app_calibration_data_t;

// ------------------------------------------------------------------------------------
// Section 2.6: Global Variables
// ------------------------------------------------------------------------------------
volatile bool g_is_scanning = false;
volatile bool g_led_state = false; // Tracks indicator LED state
volatile bool g_illum_led_state = false; // Tracks illumination LED state
volatile bool color_change_interrupt_flag = false;
DFRobot_TCS3430 tcs3430_sensor;
uint32_t last_sensor_read_time = 0;
char logBuffer[256];
web_ui_data_t g_web_data;
app_calibration_data_t current_app_calibration;
Preferences preferences_calib;
AsyncWebServer server(80);
AsyncWebSocket ws("/ws");
SemaphoreHandle_t printSemaphore = xSemaphoreCreateMutex();
volatile bool wifiConnected = false;
reference_color_t color_database[MAX_DULUX_COLORS];
int COLOR_DB_SIZE = 0;

// ------------------------------------------------------------------------------------
// Section 2.7: Forward Declarations
// ------------------------------------------------------------------------------------
xyz_color_s srgb_to_xyz(const rgb_color_s &srgb);
void initialize_color_database();
bool calibration_save();
rgb_color_s xyz_to_srgb_enhanced(const xyz_color_s &xyz_sensor_raw, float normalization_factor);
void onWebSocketEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type, void *arg, uint8_t *data, size_t len);
void log_message(int level, const char* tag, const char* message);
bool init_sensor();
void calibration_init();
void broadcast_data_update();
String getWiFiStatusString(wl_status_t status);
void attemptWiFiReconnect();
bool perform_white_balance_calibration();
void apply_ir_compensation(xyz_color_s &xyz_data, uint16_t ir1_raw, uint16_t ir2_raw, float k_ir_comp_factor);
bool check_sensor_health();

// ------------------------------------------------------------------------------------
// Section 3: Web Server Handlers
// ------------------------------------------------------------------------------------
void handleRoot(AsyncWebServerRequest *request) {
    if (LittleFS.exists("/index.html")) {
        request->send(LittleFS, "/index.html", "text/html");
    } else {
        request->send(500, "text/plain", "index.html not found. Did you upload the data folder?");
    }
}

void handleStyleCss(AsyncWebServerRequest *request) {
    if (LittleFS.exists("/style.css")) {
        request->send(LittleFS, "/style.css", "text/css");
    } else {
        request->send(404, "text/plain", "style.css not found.");
    }
}

void handleFullData(AsyncWebServerRequest *request) {
    JsonDocument doc;
    doc["data_ready"] = g_web_data.data_ready;
    doc["measured_r"] = g_web_data.measured_r;
    doc["measured_g"] = g_web_data.measured_g;
    doc["measured_b"] = g_web_data.measured_b;
    doc["matched_name"] = g_web_data.matched_name;
    doc["matched_r"] = g_web_data.matched_r;
    doc["matched_g"] = g_web_data.matched_g;
    doc["matched_b"] = g_web_data.matched_b;
    doc["delta_e"] = g_web_data.delta_e;
    doc["confidence"] = g_web_data.confidence;
    doc["is_scanning"] = g_is_scanning;
    doc["avg_x"] = g_web_data.avg_x;
    doc["avg_y"] = g_web_data.avg_y;
    doc["avg_z"] = g_web_data.avg_z;
    doc["avg_l"] = g_web_data.avg_l;
    doc["avg_a"] = g_web_data.avg_a;
    doc["avg_b"] = g_web_data.avg_b;
    doc["avg_ir1"] = g_web_data.avg_ir1;
    doc["avg_ir2"] = g_web_data.avg_ir2;
    doc["calib_gain"] = current_app_calibration.als_gain;
    doc["calib_ir_comp"] = current_app_calibration.k_ir_compensation_factor;
    doc["calib_norm"] = current_app_calibration.srgb_output_norm_factor;
    doc["led_state"] = g_led_state;
    String json_response;
    serializeJson(doc, json_response);
    request->send(200, "application/json", json_response);
}

void handleGetCurrentSettings(AsyncWebServerRequest *request) {
    JsonDocument doc;
    doc["gain"] = current_app_calibration.als_gain;
    doc["ir_comp"] = current_app_calibration.k_ir_compensation_factor;
    doc["norm"] = current_app_calibration.srgb_output_norm_factor;
    doc["int_time"] = current_app_calibration.integration_time;
    doc["adaptive"] = current_app_calibration.adaptive_scaling;
    String json_response;
    serializeJson(doc, json_response);
    request->send(200, "application/json", json_response);
}

void handleSetCalibration(AsyncWebServerRequest *request) {
    bool success = true;
    if (request->hasParam("gain", true)) {
        uint8_t gain = request->getParam("gain", true)->value().toInt();
        if (gain == 1 || gain == 4 || gain == 16 || gain == 64) {
            current_app_calibration.als_gain = gain;
            tcs3430_sensor.setALSGain(gain);
        } else success = false;
    }
    if (request->hasParam("ir_comp", true)) {
        float ir_comp = request->getParam("ir_comp", true)->value().toFloat();
        if (ir_comp >= 0.0 && ir_comp <= 1.0) current_app_calibration.k_ir_compensation_factor = ir_comp; else success = false;
    }
    if (request->hasParam("norm", true)) {
        float norm = request->getParam("norm", true)->value().toFloat();
        if (norm >= 1000.0 && norm <= 100000.0) current_app_calibration.srgb_output_norm_factor = norm; else success = false;
    }
    if (request->hasParam("int_time", true)) {
        int int_time = request->getParam("int_time", true)->value().toInt();
        if (int_time >= 0xD0 && int_time <= 0xFF) {
            current_app_calibration.integration_time = int_time;
            tcs3430_sensor.setIntegrationTime(int_time);
        } else success = false;
    }
    if (request->hasParam("adaptive", true)) {
        current_app_calibration.adaptive_scaling = request->getParam("adaptive", true)->value().toInt() != 0;
    }
    if (success) {
        current_app_calibration.data_is_valid = true;
        calibration_save();
        log_message(LOG_LEVEL_INFO, "WebCalib", "Calibration updated and saved.");
        request->send(200, "text/plain", "Saved!");
    } else {
        log_message(LOG_LEVEL_ERROR, "WebCalib", "Invalid calibration data.");
        request->send(400, "text/plain", "Invalid Value!");
    }
}

void handleStartScan(AsyncWebServerRequest *request) {
    if (!g_is_scanning) {
        g_is_scanning = true;
        g_led_state = true;
        g_illum_led_state = true;
        digitalWrite(INDICATOR_LED_PIN, HIGH);
        digitalWrite(ILLUMINATION_LED_PIN, HIGH);
        g_web_data.data_ready = false;
        strcpy(g_web_data.matched_name, "Scanning...");
        strcpy(g_web_data.confidence, "N/A");
        log_message(LOG_LEVEL_INFO, "Scan", "Scan started, LEDs ON");
        request->send(200, "text/plain", "Scan started");
    } else {
        request->send(400, "text/plain", "Scan already in progress");
    }
}

void handleStopScan(AsyncWebServerRequest *request) {
    if (g_is_scanning) {
        g_is_scanning = false;
        g_led_state = false;
        g_illum_led_state = false;
        digitalWrite(INDICATOR_LED_PIN, LOW);
        digitalWrite(ILLUMINATION_LED_PIN, LOW);
        g_web_data.data_ready = false;
        strcpy(g_web_data.matched_name, "Scan stopped");
        strcpy(g_web_data.confidence, "N/A");
        log_message(LOG_LEVEL_INFO, "Scan", "Scan stopped, LEDs OFF");
        request->send(200, "text/plain", "Scan stopped");
    } else {
        request->send(400, "text/plain", "No scan in progress");
    }
}

void handleToggleLed(AsyncWebServerRequest *request) {
    g_led_state = !g_led_state;
    digitalWrite(INDICATOR_LED_PIN, g_led_state ? HIGH : LOW);
    log_message(LOG_LEVEL_INFO, "LED", g_led_state ? "Indicator LED ON" : "Indicator LED OFF");
    JsonDocument response;
    response["led_state"] = g_led_state;
    String json_response;
    serializeJson(response, json_response);
    request->send(200, "application/json", json_response);
}

void handleWhiteBalanceCalibration(AsyncWebServerRequest *request) {
    if (!g_is_scanning) {
        g_led_state = true;
        digitalWrite(INDICATOR_LED_PIN, HIGH);
        if (perform_white_balance_calibration()) {
            calibration_save();
            log_message(LOG_LEVEL_INFO, "WebCalib", "White balance calibration completed.");
            request->send(200, "text/plain", "Calibration completed!");
        } else {
            log_message(LOG_LEVEL_ERROR, "WebCalib", "Calibration failed.");
            request->send(400, "text/plain", "Calibration failed: insufficient light or sensor error.");
        }
        g_led_state = false;
        digitalWrite(INDICATOR_LED_PIN, LOW);
    } else {
        request->send(400, "text/plain", "Cannot calibrate while scanning");
    }
}

void handleGetPreviewData(AsyncWebServerRequest *request) {
    JsonDocument doc;
    doc["current_rgb"][0] = g_web_data.measured_r;
    doc["current_rgb"][1] = g_web_data.measured_g;
    doc["current_rgb"][2] = g_web_data.measured_b;
    doc["new_rgb"][0] = g_web_data.measured_r;
    doc["new_rgb"][1] = g_web_data.measured_g;
    doc["new_rgb"][2] = g_web_data.measured_b;
    doc["current_xyz"][0] = g_web_data.avg_x;
    doc["current_xyz"][1] = g_web_data.avg_y;
    doc["current_xyz"][2] = g_web_data.avg_z;
    doc["new_xyz"][0] = g_web_data.avg_x;
    doc["new_xyz"][1] = g_web_data.avg_y;
    doc["new_xyz"][2] = g_web_data.avg_z;
    String json_response;
    serializeJson(doc, json_response);
    request->send(200, "application/json", json_response);
}

void handleSetAdvancedSettings(AsyncWebServerRequest *request) {
    bool success = true;
    if (request->hasParam("integration_time", true)) {
        String val = request->getParam("integration_time", true)->value();
        uint8_t integration_time = (uint8_t) strtol(val.c_str(), nullptr, 16);
        if (integration_time == 0x01 || integration_time == 0x11 || integration_time == 0x23 || integration_time == 0x40 || integration_time == 0xFF) {
            current_app_calibration.integration_time = integration_time;
            tcs3430_sensor.setIntegrationTime(integration_time);
        } else {
            success = false;
        }
    } else {
        success = false;
    }
    if (request->hasParam("adaptive_scaling", true)) {
        String val = request->getParam("adaptive_scaling", true)->value();
        if (val == "true") {
            current_app_calibration.adaptive_scaling = true;
        } else if (val == "false") {
            current_app_calibration.adaptive_scaling = false;
        } else {
            success = false;
        }
    } else {
        success = false;
    }
    if (success) {
        calibration_save();
        request->send(200, "text/plain", "Advanced settings saved");
    } else {
        request->send(400, "text/plain", "Invalid parameters");
    }
}

void setupServerRoutes() {
    server.on("/", HTTP_GET, handleRoot);
    server.on("/style.css", HTTP_GET, handleStyleCss);
    server.on("/fulldata", HTTP_GET, handleFullData);
    server.on("/get_current_settings", HTTP_GET, handleGetCurrentSettings);
    server.on("/set_calibration", HTTP_POST, handleSetCalibration);
    server.on("/start_scan", HTTP_POST, handleStartScan);
    server.on("/stop_scan", HTTP_POST, handleStopScan);
    server.on("/toggle_led", HTTP_POST, handleToggleLed);
    server.on("/white_balance", HTTP_POST, handleWhiteBalanceCalibration);
    server.on("/get_preview_data", HTTP_GET, handleGetPreviewData);
    server.on("/set_advanced_settings", HTTP_POST, handleSetAdvancedSettings);
    ws.onEvent(onWebSocketEvent);
    server.addHandler(&ws);
    server.begin();
    log_message(LOG_LEVEL_INFO, "Web", "Server routes configured.");
}

// ------------------------------------------------------------------------------------
// Section 4: Color Conversion
// ------------------------------------------------------------------------------------
const xyz_color_s D65_WHITEPOINT = {95.047f, 100.0f, 108.883f};
const float MATRIX_XYZ_TO_SRGB[3][3] = {
    { 3.2404542f, -1.5371385f, -0.4985314f},
    {-0.9692660f,  1.8760108f,  0.0415560f},
    { 0.0556434f, -0.2040259f,  1.0572252f}
};
const float MATRIX_SRGB_TO_XYZ[3][3] = {
    {0.4124564f, 0.3575761f, 0.1804375f},
    {0.2126729f, 0.7151522f, 0.0721750f},
    {0.0193339f, 0.1191920f, 0.9503041f}
};

float srgb_gamma_correct(float linear_val) {
    if (linear_val <= 0.0f) return 0.0f;
    if (linear_val >= 1.0f) return 1.0f;
    if (linear_val <= 0.0031308f) return 12.92f * linear_val;
    return 1.055f * powf(linear_val, 1.0f / 2.4f) - 0.055f;
}

float srgb_inverse_gamma_correct(float srgb_val) {
    if (srgb_val <= 0.0f) return 0.0f;
    if (srgb_val >= 1.0f) return 1.0f;
    if (srgb_val <= 0.04045f) return srgb_val / 12.92f;
    return powf((srgb_val + 0.055f) / 1.055f, 2.4f);
}

float f_lab(float t) {
    const float epsilon = 216.0f / 24389.0f;
    const float kappa = 24389.0f / 27.0f;
    if (t > epsilon) return powf(t, 1.0f / 3.0f);
    return (kappa * t + 16.0f) / 116.0f;
}

xyz_color_s srgb_to_xyz(const rgb_color_s &srgb) {
    float r_linear = srgb_inverse_gamma_correct(srgb.r / 255.0f);
    float g_linear = srgb_inverse_gamma_correct(srgb.g / 255.0f);
    float b_linear = srgb_inverse_gamma_correct(srgb.b / 255.0f);
    xyz_color_s xyz_out;
    xyz_out.x = (MATRIX_SRGB_TO_XYZ[0][0] * r_linear + MATRIX_SRGB_TO_XYZ[0][1] * g_linear + MATRIX_SRGB_TO_XYZ[0][2] * b_linear) * 100.0f;
    xyz_out.y = (MATRIX_SRGB_TO_XYZ[1][0] * r_linear + MATRIX_SRGB_TO_XYZ[1][1] * g_linear + MATRIX_SRGB_TO_XYZ[1][2] * b_linear) * 100.0f;
    xyz_out.z = (MATRIX_SRGB_TO_XYZ[2][0] * r_linear + MATRIX_SRGB_TO_XYZ[2][1] * g_linear + MATRIX_SRGB_TO_XYZ[2][2] * b_linear) * 100.0f;
    return xyz_out;
}

void xyz_to_lab(const xyz_color_s &xyz_in, lab_color_s &lab_out, const xyz_color_s &white_point_ref) {
    float xr = xyz_in.x / white_point_ref.x;
    float yr = xyz_in.y / white_point_ref.y;
    float zr = xyz_in.z / white_point_ref.z;
    float fx = f_lab(xr);
    float fy = f_lab(yr);
    float fz = f_lab(zr);
    lab_out.l = 116.0f * fy - 16.0f;
    lab_out.a = 500.0f * (fx - fy);
    lab_out.b = 200.0f * (fy - fz);
}

float delta_e_cie76(const lab_color_s &lab1, const lab_color_s &lab2) {
    return sqrtf(powf(lab1.l - lab2.l, 2) + powf(lab1.a - lab2.a, 2) + powf(lab1.b - lab2.b, 2));
}

rgb_color_s xyz_to_srgb_enhanced(const xyz_color_s &xyz_sensor_raw, float normalization_factor) {
    rgb_color_s rgb_out;
    if (normalization_factor <= 1e-6) { normalization_factor = 15000.0f; }
    float adaptive_scale = 1.0f;
    if (current_app_calibration.adaptive_scaling) {
        float luminance = xyz_sensor_raw.y;
        if (luminance > 0) {
            adaptive_scale = fmin(2.0f, fmax(0.5f, 10000.0f / luminance));
        }
    }
    float effective_norm = normalization_factor * adaptive_scale;
    float X_norm = xyz_sensor_raw.x / effective_norm;
    float Y_norm = xyz_sensor_raw.y / effective_norm;
    float Z_norm = xyz_sensor_raw.z / effective_norm;
    float R_linear = MATRIX_XYZ_TO_SRGB[0][0] * X_norm + MATRIX_XYZ_TO_SRGB[0][1] * Y_norm + MATRIX_XYZ_TO_SRGB[0][2] * Z_norm;
    float G_linear = MATRIX_XYZ_TO_SRGB[1][0] * X_norm + MATRIX_XYZ_TO_SRGB[1][1] * Y_norm + MATRIX_XYZ_TO_SRGB[1][2] * Z_norm;
    float B_linear = MATRIX_XYZ_TO_SRGB[2][0] * X_norm + MATRIX_XYZ_TO_SRGB[2][1] * Y_norm + MATRIX_XYZ_TO_SRGB[2][2] * Z_norm;
    rgb_out.r = (uint8_t)constrain(srgb_gamma_correct(fmax(0.0f, R_linear)) * 255.0f, 0.0f, 255.0f);
    rgb_out.g = (uint8_t)constrain(srgb_gamma_correct(fmax(0.0f, G_linear)) * 255.0f, 0.0f, 255.0f);
    rgb_out.b = (uint8_t)constrain(srgb_gamma_correct(fmax(0.0f, B_linear)) * 255.0f, 0.0f, 255.0f);
    return rgb_out;
}

void apply_ir_compensation(xyz_color_s &xyz_data, uint16_t ir1_raw, uint16_t ir2_raw, float k_ir_comp_factor) {
    float ir_effect = k_ir_comp_factor * ((float)ir1_raw + (float)ir2_raw) / 2.0f;
    xyz_data.x = fmax(0.0f, xyz_data.x - ir_effect);
    xyz_data.y = fmax(0.0f, xyz_data.y - ir_effect);
    xyz_data.z = fmax(0.0f, xyz_data.z - ir_effect);
}

// ------------------------------------------------------------------------------------
// Section 5: Color Database
// ------------------------------------------------------------------------------------
bool loadDuluxDatabase() {
    log_message(LOG_LEVEL_INFO, "ColorDB", "Loading Dulux color database from LittleFS...");
    if (!LittleFS.exists("/dulux.json")) {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "dulux.json not found! Upload data via 'ESP32 Sketch Data Upload'");
        return false;
    }

    File file = LittleFS.open("/dulux.json", "r");
    if (!file) {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "Failed to open dulux.json file.");
        return false;
    }

    // Only parse required fields for memory efficiency
    JsonDocument filter;
    filter[0]["name"] = true;
    filter[0]["r"] = true;
    filter[0]["g"] = true;
    filter[0]["b"] = true;

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file, DeserializationOption::Filter(filter));
    file.close();

    if (error) {
        char msg[100];
        snprintf(msg, sizeof(msg), "deserializeJson() failed: %s", error.c_str());
        log_message(LOG_LEVEL_ERROR, "ColorDB", msg);
        return false;
    }

    if (doc.isNull()) {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "JSON document is null. File is likely empty or malformed.");
        return false;
    }

    JsonArray colorArray = doc.as<JsonArray>();
    COLOR_DB_SIZE = 0;
    for (JsonVariant v : colorArray) {
        if (COLOR_DB_SIZE >= MAX_DULUX_COLORS) {
            log_message(LOG_LEVEL_WARN, "ColorDB", "Max color limit reached. Some colors not loaded.");
            break;
        }

        JsonObject color = v.as<JsonObject>();
        if (color.isNull() || !color["name"].is<const char*>() || !color["r"].is<int>() || !color["g"].is<int>() || !color["b"].is<int>()) {
            log_message(LOG_LEVEL_WARN, "ColorDB", "Skipping invalid/incomplete color object in JSON.");
            continue;
        }

        strncpy(color_database[COLOR_DB_SIZE].name, color["name"], MAX_COLOR_NAME_LEN - 1);
        color_database[COLOR_DB_SIZE].name[MAX_COLOR_NAME_LEN - 1] = '\0';
        color_database[COLOR_DB_SIZE].rgb_ref.r = color["r"];
        color_database[COLOR_DB_SIZE].rgb_ref.g = color["g"];
        color_database[COLOR_DB_SIZE].rgb_ref.b = color["b"];
        color_database[COLOR_DB_SIZE].is_valid = true;
        COLOR_DB_SIZE++;
    }

    if (COLOR_DB_SIZE > 0) {
        char msg[120];
        // Use memoryUsage() for ArduinoJson v7 and above
        snprintf(msg, sizeof(msg), "Loaded %d colors. JSON memory usage: %zu bytes",
                 COLOR_DB_SIZE, doc.memoryUsage());
        log_message(LOG_LEVEL_INFO, "ColorDB", msg);

        initialize_color_database();
        return true;
    } else {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "No valid colors found in dulux.json");
        return false;
    }
}

void initialize_color_database() {
    if (COLOR_DB_SIZE == 0) {
        log_message(LOG_LEVEL_WARN, "ColorDB", "No colors loaded. Using built-in fallback.");
        const reference_color_t builtin_colors[] = {
            { "Jasmine White", {245, 240, 220}, {0,0,0}, false },
            { "Almond White", {240, 235, 210}, {0,0,0}, false },
            { "Primrose White", {242, 238, 215}, {0,0,0}, false },
            { "Fine Cream", {238, 230, 210}, {0,0,0}, false },
        };
        const int BUILTIN_COLORS = sizeof(builtin_colors) / sizeof(builtin_colors[0]);

        for (int i = 0; i < BUILTIN_COLORS && i < MAX_DULUX_COLORS; i++) {
            memcpy(&color_database[i], &builtin_colors[i], sizeof(reference_color_t));
            color_database[i].is_valid = true;
            COLOR_DB_SIZE++;
        }
    }
    log_message(LOG_LEVEL_INFO, "ColorDB", "Pre-calculating LAB values for the database...");
    for (int i = 0; i < COLOR_DB_SIZE; i++) {
        if (color_database[i].is_valid) {
            xyz_to_lab(srgb_to_xyz(color_database[i].rgb_ref), color_database[i].lab_ref, D65_WHITEPOINT);
        }
    }
    log_message(LOG_LEVEL_INFO, "ColorDB", "LAB value calculation complete.");
}

int find_closest_color_in_db(const lab_color_s &measured_lab, float &out_delta_e) {
    float min_delta_e = 999999.0f;
    int closest_idx = -1;
    for (int i = 0; i < COLOR_DB_SIZE; i++) {
        if (color_database[i].is_valid) {
            float current_delta_e = delta_e_cie76(measured_lab, color_database[i].lab_ref);
            if (current_delta_e < min_delta_e) {
                min_delta_e = current_delta_e;
                closest_idx = i;
            }
        }
    }
    out_delta_e = min_delta_e;
    return closest_idx;
}
// ------------------------------------------------------------------------------------
// Section 6: Calibration and Sensor Functions
// ------------------------------------------------------------------------------------
bool calibration_load() {
    if (!preferences_calib.begin("clr_calib", true)) {
        log_message(LOG_LEVEL_WARN, "Calib", "Failed to open NVS namespace. Using defaults.");
        current_app_calibration = { 0.02f, 15000.0f, 16, 0x23, true, false };
        return false;
    }
    if (preferences_calib.isKey("cal_data")) {
        size_t bytes = preferences_calib.getBytes("cal_data", &current_app_calibration, sizeof(app_calibration_data_t));
        preferences_calib.end();
        if (bytes != sizeof(app_calibration_data_t)) {
            log_message(LOG_LEVEL_WARN, "Calib", "NVS data size mismatch. Using defaults.");
            current_app_calibration = { 0.02f, 15000.0f, 16, 0x23, true, false };
            return false;
        }
        log_message(LOG_LEVEL_INFO, "Calib", "Calibration loaded from NVS.");
        return true;
    }
    log_message(LOG_LEVEL_INFO, "Calib", "No calib data in NVS. Using defaults.");
    preferences_calib.end();
    current_app_calibration = { 0.02f, 15000.0f, 16, 0x23, true, false };
    return false;
}

bool calibration_save() {
    if (!preferences_calib.begin("clr_calib", false)) {
        log_message(LOG_LEVEL_ERROR, "Calib", "Failed to open NVS namespace for writing.");
        return false;
    }
    size_t bytes = preferences_calib.putBytes("cal_data", &current_app_calibration, sizeof(app_calibration_data_t));
    preferences_calib.end();
    if (bytes == sizeof(app_calibration_data_t)) {
        log_message(LOG_LEVEL_INFO, "Calib", "Calibration data saved.");
        return true;
    }
    log_message(LOG_LEVEL_ERROR, "Calib", "Failed to save calibration data to NVS.");
    return false;
}

void calibration_init() {
    calibration_load();
    tcs3430_sensor.setALSGain(current_app_calibration.als_gain);
    tcs3430_sensor.setIntegrationTime(current_app_calibration.integration_time);
    tcs3430_sensor.setWaitTimer(true);
    tcs3430_sensor.setWaitTime(0x00);
    log_message(LOG_LEVEL_INFO, "Calib", "Sensor configured with loaded calibration.");
}

bool perform_white_balance_calibration() {
    log_message(LOG_LEVEL_INFO, "Calibration", "Starting white balance calibration...");
    digitalWrite(ILLUMINATION_LED_PIN, HIGH);
    g_illum_led_state = true;
    log_message(LOG_LEVEL_INFO, "Calibration", "Illumination LED ON");
    uint8_t tcs_address = 0x39;
    Wire.beginTransmission(tcs_address);
    Wire.write(0x06); Wire.write(0xE0);
    if (Wire.endTransmission() != 0) {
        log_message(LOG_LEVEL_ERROR, "Calibration", "Failed to clear interrupts");
        digitalWrite(ILLUMINATION_LED_PIN, LOW);
        g_illum_led_state = false;
        return false;
    }
    Wire.beginTransmission(tcs_address);
    Wire.write(0x00); Wire.write(0x03);
    if (Wire.endTransmission() != 0) {
        log_message(LOG_LEVEL_ERROR, "Calibration", "Failed to enable sensor");
        digitalWrite(ILLUMINATION_LED_PIN, LOW);
        g_illum_led_state = false;
        return false;
    }
    vTaskDelay(pdMS_TO_TICKS(100));
    uint8_t status = tcs3430_sensor.getDeviceStatus();
    if (!(status & 0x03)) {
        char status_msg[100];
        snprintf(status_msg, sizeof(status_msg), "Sensor not enabled: 0x%02X (PON:%d, AEN:%d)", status, (status & 0x01), (status & 0x02) >> 1);
        log_message(LOG_LEVEL_ERROR, "Calibration", status_msg);
        digitalWrite(ILLUMINATION_LED_PIN, LOW);
        g_illum_led_state = false;
        return false;
    }
    const int num_samples = 10;
    float sum_x = 0, sum_y = 0, sum_z = 0;
    for (int i = 0; i < num_samples; i++) {
        vTaskDelay(pdMS_TO_TICKS(100));
        sum_x += tcs3430_sensor.getXData();
        sum_y += tcs3430_sensor.getYData();
        sum_z += tcs3430_sensor.getZData();
    }
    float avg_x = sum_x / num_samples;
    float avg_y = sum_y / num_samples;
    float avg_z = sum_z / num_samples;
    digitalWrite(ILLUMINATION_LED_PIN, LOW);
    g_illum_led_state = false;
    log_message(LOG_LEVEL_INFO, "Calibration", "Illumination LED OFF");
    if (avg_y > 50.0f) {
        current_app_calibration.srgb_output_norm_factor = avg_y * 0.8f;
        log_message(LOG_LEVEL_INFO, "Calibration", "White balance calibration completed successfully.");
        char msg[100];
        snprintf(msg, sizeof(msg), "New normalization factor: %.1f (X:%.1f, Y:%.1f, Z:%.1f)", current_app_calibration.srgb_output_norm_factor, avg_x, avg_y, avg_z);
        log_message(LOG_LEVEL_INFO, "Calibration", msg);
        return true;
    } else {
        char msg[100];
        snprintf(msg, sizeof(msg), "Insufficient light for calibration (X:%.1f, Y:%.1f, Z:%.1f)", avg_x, avg_y, avg_z);
        log_message(LOG_LEVEL_WARN, "Calibration", msg);
        return false;
    }
}

bool init_sensor() {
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN, 100000);
    uint8_t tcs_address = 0x39;
    Wire.beginTransmission(tcs_address);
    int error = Wire.endTransmission();
    if (error != 0) {
        char err_msg[50];
        snprintf(err_msg, sizeof(err_msg), "No TCS3430 at 0x%02X. Error: %d", tcs_address, error);
        log_message(LOG_LEVEL_ERROR, "Sensor", err_msg);
        return false;
    }
    Wire.beginTransmission(tcs_address);
    Wire.write(0x18);
    Wire.endTransmission();
    Wire.requestFrom(tcs_address, (uint8_t)1);
    if (Wire.available()) {
        uint8_t device_id = Wire.read();
        if (device_id != 0xD0) {
            char id_msg[50];
            snprintf(id_msg, sizeof(id_msg), "Invalid TCS3430 ID: 0x%02X (expected 0xD0)", device_id);
            log_message(LOG_LEVEL_ERROR, "Sensor", id_msg);
            return false;
        }
        char addr_msg[50];
        snprintf(addr_msg, sizeof(addr_msg), "Found TCS3430 at 0x%02X, ID: 0x%02X", tcs_address, device_id);
        log_message(LOG_LEVEL_INFO, "Sensor", addr_msg);
    } else {
        log_message(LOG_LEVEL_ERROR, "Sensor", "Failed to read TCS3430 ID");
        return false;
    }
    for (int attempt = 1; attempt <= 3; attempt++) {
        char msg[50];
        snprintf(msg, sizeof(msg), "Attempt %d", attempt);
        log_message(LOG_LEVEL_INFO, "Sensor", msg);
        Wire.beginTransmission(tcs_address);
        Wire.write(0x06); Wire.write(0xE0);
        if (Wire.endTransmission() != 0) {
            log_message(LOG_LEVEL_ERROR, "Sensor", "I2C failed during interrupt clear");
            vTaskDelay(pdMS_TO_TICKS(500));
            continue;
        }
        Wire.beginTransmission(tcs_address);
        Wire.write(0x00); Wire.write(0x01);
        if (Wire.endTransmission() != 0) {
            log_message(LOG_LEVEL_ERROR, "Sensor", "I2C failed during power-on");
            vTaskDelay(pdMS_TO_TICKS(500));
            continue;
        }
        vTaskDelay(pdMS_TO_TICKS(10));
        Wire.beginTransmission(tcs_address);
        Wire.write(0x00); Wire.write(0x03);
        if (Wire.endTransmission() != 0) {
            log_message(LOG_LEVEL_ERROR, "Sensor", "I2C failed during ADC enable");
            vTaskDelay(pdMS_TO_TICKS(500));
            continue;
        }
        vTaskDelay(pdMS_TO_TICKS(100));
        if (tcs3430_sensor.begin()) {
            uint8_t status = tcs3430_sensor.getDeviceStatus();
            char status_msg[100];
            snprintf(status_msg, sizeof(status_msg), "Sensor status: 0x%02X (PON:%d, AEN:%d, ASAT:%d, AINT:%d)",
                     status, (status & 0x01), (status & 0x02) >> 1, (status & 0x08) >> 3, (status & 0x10) >> 4);
            log_message(LOG_LEVEL_INFO, "Sensor", status_msg);
            log_message(LOG_LEVEL_INFO, "Sensor", "TCS3430 initialized successfully.");
            return true;
        }
        snprintf(msg, sizeof(msg), "Attempt %d failed.", attempt);
        log_message(LOG_LEVEL_WARN, "Sensor", msg);
        vTaskDelay(pdMS_TO_TICKS(500));
    }
    log_message(LOG_LEVEL_ERROR, "Sensor", "TCS3430 init failed after 3 attempts.");
    return false;
}

bool check_sensor_health() {
    uint8_t status = tcs3430_sensor.getDeviceStatus();
    uint16_t x_val = tcs3430_sensor.getXData();
    uint16_t y_val = tcs3430_sensor.getYData();
    uint16_t z_val = tcs3430_sensor.getZData();
    if (!(status & 0x03)) {
        uint8_t tcs_address = 0x39;
        Wire.beginTransmission(tcs_address);
        Wire.write(0x06); Wire.write(0xE0);
        if (Wire.endTransmission() != 0) {
            log_message(LOG_LEVEL_ERROR, "Health", "Failed to clear interrupts");
        } else {
            Wire.beginTransmission(tcs_address);
            Wire.write(0x00); Wire.write(0x03);
            if (Wire.endTransmission() == 0) {
                vTaskDelay(pdMS_TO_TICKS(100));
                status = tcs3430_sensor.getDeviceStatus();
                log_message(LOG_LEVEL_INFO, "Health", "Re-enabled sensor due to PON=0 or AEN=0");
            } else {
                log_message(LOG_LEVEL_ERROR, "Health", "Failed to re-enable sensor");
            }
        }
    }
    if (status == 0xFF) {
        char status_msg[50];
        snprintf(status_msg, sizeof(status_msg), "Critical sensor failure: 0x%02X", status);
        log_message(LOG_LEVEL_ERROR, "Health", status_msg);
        return false;
    }
    static uint32_t last_health_log = 0;
    if (millis() - last_health_log > 10000) {
        char health_msg[100];
        snprintf(health_msg, sizeof(health_msg), "Sensor Health - Status:0x%02X (PON:%d, AEN:%d, ASAT:%d, AINT:%d), X:%d, Y:%d, Z:%d",
                 status, (status & 0x01), (status & 0x02) >> 1, (status & 0x08) >> 3, (status & 0x10) >> 4, x_val, y_val, z_val);
        log_message(LOG_LEVEL_INFO, "Health", health_msg);
        if (status == 0x00 || (status & 0x10)) {
            char warn_msg[50];
            snprintf(warn_msg, sizeof(warn_msg), "Non-standard status: 0x%02X - likely no object", status);
            log_message(LOG_LEVEL_INFO, "Health", warn_msg);
        }
        last_health_log = millis();
    }
    return true;
}

// ------------------------------------------------------------------------------------
// Section 7: Network & System Tasks
// ------------------------------------------------------------------------------------
void onWebSocketEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type, void *arg, uint8_t *data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT: {
            char msg[50];
            snprintf(msg, sizeof(msg), "Client connected, ID: %u", client->id());
            log_message(LOG_LEVEL_INFO, "WebSocket", msg);
            break;
        }
        case WS_EVT_DISCONNECT: {
            char msg[50];
            snprintf(msg, sizeof(msg), "Client disconnected, ID: %u", client->id());
            log_message(LOG_LEVEL_INFO, "WebSocket", msg);
            break;
        }
        case WS_EVT_DATA:
            // Optionally, handle incoming data here.
            break;
        case WS_EVT_PONG:
            // Optionally, handle pong events here.
            break;
        case WS_EVT_ERROR: {
            char msg[80];
            snprintf(msg, sizeof(msg), "Error on client ID %u", client->id());
            log_message(LOG_LEVEL_ERROR, "WebSocket", msg);
            break;
        }
    }
}

String getWiFiStatusString(wl_status_t status) {
    switch (status) {
        case WL_NO_SHIELD: return "No WiFi Shield";
        case WL_IDLE_STATUS: return "Idle";
        case WL_NO_SSID_AVAIL: return "No SSID Available";
        case WL_SCAN_COMPLETED: return "Scan Completed";
        case WL_CONNECTED: return "Connected";
        case WL_CONNECT_FAILED: return "Connection Failed";
        case WL_CONNECTION_LOST: return "Connection Lost";
        case WL_DISCONNECTED: return "Disconnected";
        default: return String("Unknown (") + String(status) + ")";
    }
}

void attemptWiFiReconnect() {
    if (WiFi.status() != WL_CONNECTED && wifiConnected) {
        log_message(LOG_LEVEL_INFO, "WiFi", "Disconnected, attempting reconnect...");
        WiFi.reconnect();
        unsigned long start = millis();
        while (WiFi.status() != WL_CONNECTED && millis() - start < 5000) {
            vTaskDelay(pdMS_TO_TICKS(250));
        }
        if (WiFi.status() == WL_CONNECTED) {
            wifiConnected = true;
            char msg[50];
            snprintf(msg, sizeof(msg), "Reconnected, IP: %s", WiFi.localIP().toString().c_str());
            log_message(LOG_LEVEL_INFO, "WiFi", msg);
        } else {
            wifiConnected = false;
            char msg[50];
            snprintf(msg, sizeof(msg), "Reconnect failed, Status: %s", getWiFiStatusString(WiFi.status()).c_str());
            log_message(LOG_LEVEL_ERROR, "WiFi", msg);
        }
    }
}

bool initWiFi() {
    WiFi.mode(WIFI_STA);
    WiFi.config(STATIC_IP, GATEWAY, SUBNET, DNS_SERVER_PRIMARY, DNS_SERVER_SECONDARY);
    WiFi.begin(SSID, PASSWORD);
    log_message(LOG_LEVEL_INFO, "WiFi", "Connecting with static IP...");
    if (WiFi.waitForConnectResult(8000) != WL_CONNECTED) {
        log_message(LOG_LEVEL_WARN, "WiFi", "Static IP failed. Trying DHCP...");
        WiFi.disconnect(true);
        WiFi.begin(SSID, PASSWORD);
        if (WiFi.waitForConnectResult(8000) != WL_CONNECTED) {
            log_message(LOG_LEVEL_ERROR, "WiFi", "Connection failed.");
            return false;
        }
    }
    wifiConnected = true;
    char msg[60];
    snprintf(msg, sizeof(msg), "Connected. IP: %s", WiFi.localIP().toString().c_str());
    log_message(LOG_LEVEL_INFO, "WiFi", msg);
    return true;
}

void TaskNetwork(void *pvParameters) {
    bool otaInProgress = false;
    if (initWiFi()) {
        setupServerRoutes();
        ArduinoOTA.setHostname("ColorMatcher");
        ArduinoOTA.onStart([&]() {
            otaInProgress = true;
            log_message(LOG_LEVEL_INFO, "OTA", "Update started");
        });
        ArduinoOTA.onEnd([&]() {
            otaInProgress = false;
            log_message(LOG_LEVEL_INFO, "OTA", "Update completed");
        });
        ArduinoOTA.onError([&](ota_error_t error) {
            otaInProgress = false;
            char msg[50];
            snprintf(msg, sizeof(msg), "Error: %d", error);
            log_message(LOG_LEVEL_ERROR, "OTA", msg);
        });
        ArduinoOTA.onProgress([](unsigned int progress, unsigned int total) {
            // Optionally, handle OTA progress here.
        });
        ArduinoOTA.begin();
        log_message(LOG_LEVEL_INFO, "OTA", "Initialized");
    }
    unsigned long lastWiFiCheck = 0;
    unsigned long lastOTARefresh = 0;
    for (;;) {
        unsigned long currentMillis = millis();
        if (currentMillis - lastWiFiCheck >= WIFI_CHECK_INTERVAL) {
            if (!wifiConnected) {
                log_message(LOG_LEVEL_INFO, "Network", "WiFi disconnected. Attempting reconnect...");
                initWiFi();
            } else {
                attemptWiFiReconnect();
            }
            lastWiFiCheck = currentMillis;
        }
        if (wifiConnected) {
            ArduinoOTA.handle();
            if (!otaInProgress && currentMillis - lastOTARefresh >= OTA_REFRESH_INTERVAL) {
                ws.cleanupClients();
                lastOTARefresh = currentMillis;
            }
        }
        vTaskDelay(pdMS_TO_TICKS(2000));
    }
}

void IRAM_ATTR handle_sensor_interrupt() {
    color_change_interrupt_flag = true;
}
// ------------------------------------------------------------------------------------
// Section 8: Main Application
// ------------------------------------------------------------------------------------
void log_message(int level, const char* tag, const char* message) {
    if (level > CURRENT_LOG_LEVEL) return;
    snprintf(logBuffer, sizeof(logBuffer), "[%s] %s", tag, message);
    if (xSemaphoreTake(printSemaphore, portMAX_DELAY) == pdTRUE) {
        Serial.println(logBuffer);
        if (wifiConnected && ws.count() > 0) {
            ws.textAll(logBuffer);
        }
        xSemaphoreGive(printSemaphore);
    }
}

void broadcast_data_update() {
    if (wifiConnected && ws.count() > 0) {
        JsonDocument doc;
        doc["type"] = "data_update";
        JsonObject payload = doc["payload"].to<JsonObject>();
        payload["data_ready"] = g_web_data.data_ready;
        payload["measured_r"] = g_web_data.measured_r;
        payload["measured_g"] = g_web_data.measured_g;
        payload["measured_b"] = g_web_data.measured_b;
        payload["matched_name"] = g_web_data.matched_name;
        payload["matched_r"] = g_web_data.matched_r;
        payload["matched_g"] = g_web_data.matched_g;
        payload["matched_b"] = g_web_data.matched_b;
        payload["delta_e"] = g_web_data.delta_e;
        payload["confidence"] = g_web_data.confidence;
        payload["avg_x"] = g_web_data.avg_x;
        payload["avg_y"] = g_web_data.avg_y;
        payload["avg_z"] = g_web_data.avg_z;
        payload["avg_l"] = g_web_data.avg_l;
        payload["avg_a"] = g_web_data.avg_a;
        payload["avg_b"] = g_web_data.avg_b;
        payload["avg_ir1"] = g_web_data.avg_ir1;
        payload["avg_ir2"] = g_web_data.avg_ir2;
        String json_string;
        serializeJson(doc, json_string);
        ws.textAll(json_string);
    }
}

void setup() {
    Serial.begin(115200);
    vTaskDelay(pdMS_TO_TICKS(1000));
    esp_reset_reason_t reason = esp_reset_reason();
    String reset_reason;
    switch (reason) {
        case ESP_RST_POWERON: reset_reason = "Power-on"; break;
        case ESP_RST_SW: reset_reason = "Software reset"; break;
        case ESP_RST_PANIC: reset_reason = "Exception/panic"; break;
        case ESP_RST_INT_WDT: reset_reason = "Interrupt watchdog"; break;
        case ESP_RST_TASK_WDT: reset_reason = "Task watchdog"; break;
        case ESP_RST_WDT: reset_reason = "Other watchdog"; break;
        case ESP_RST_DEEPSLEEP: reset_reason = "Deep sleep"; break;
        case ESP_RST_BROWNOUT: reset_reason = "Brownout"; break;
        case ESP_RST_SDIO: reset_reason = "SDIO"; break;
        default: reset_reason = String("Unknown (") + String(reason) + ")";
    }
    char msg[100];
    snprintf(msg, sizeof(msg), "Booting Color Matcher - Dulux Edition. Reset reason: %s", reset_reason.c_str());
    log_message(LOG_LEVEL_INFO, "System", msg);

    pinMode(ILLUMINATION_LED_PIN, OUTPUT);
    digitalWrite(ILLUMINATION_LED_PIN, LOW);
    pinMode(INDICATOR_LED_PIN, OUTPUT);
    digitalWrite(INDICATOR_LED_PIN, LOW);

    if (!LittleFS.begin(FORMAT_LITTLEFS_IF_FAILED)) {
        log_message(LOG_LEVEL_ERROR, "LittleFS", "Mount failed! Check partition scheme or format.");
        if (FORMAT_LITTLEFS_IF_FAILED) {
            log_message(LOG_LEVEL_INFO, "LittleFS", "Formatting...");
            if (LittleFS.format()) {
                log_message(LOG_LEVEL_INFO, "LittleFS", "Formatted successfully. Rebooting...");
                vTaskDelay(pdMS_TO_TICKS(2000));
                ESP.restart();
            } else {
                log_message(LOG_LEVEL_ERROR, "LittleFS", "Format failed.");
            }
        }
    } else {
        log_message(LOG_LEVEL_INFO, "LittleFS", "Mounted successfully.");
    }
    
    esp_err_t nvs_err = nvs_flash_init();
    if (nvs_err == ESP_ERR_NVS_NO_FREE_PAGES || nvs_err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        log_message(LOG_LEVEL_WARN, "NVS", "Initialization failed, erasing...");
        nvs_flash_erase();
        nvs_err = nvs_flash_init();
        if (nvs_err != ESP_OK) {
            log_message(LOG_LEVEL_ERROR, "NVS", "Init failed after erase.");
        }
    }
    
    g_web_data = {0};
    g_web_data.data_ready = false;
    strcpy(g_web_data.matched_name, "No Object");
    strcpy(g_web_data.confidence, "N/A");

    bool sensor_ok = false;
    for (int attempt = 1; attempt <= 3; attempt++) {
        if (init_sensor()) {
            sensor_ok = true;
            break;
        }
        char msg[50];
        snprintf(msg, sizeof(msg), "Init attempt %d failed. Retrying...", attempt);
        log_message(LOG_LEVEL_WARN, "Sensor", msg);
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    if (!sensor_ok) {
        log_message(LOG_LEVEL_ERROR, "System", "CRITICAL: SENSOR FAILED TO INIT. System halted.");
        while(1) {
            digitalWrite(INDICATOR_LED_PIN, !digitalRead(INDICATOR_LED_PIN));
            delay(100);
        }
    }
    
    calibration_init();
    
    if (!loadDuluxDatabase()) {
        log_message(LOG_LEVEL_ERROR, "System", "CRITICAL: Could not load Dulux DB. Using built-in fallback.");
        initialize_color_database();
    }

    if (SENSOR_INTERRUPT_PIN >= 0) {
        pinMode(SENSOR_INTERRUPT_PIN, INPUT_PULLUP);
        attachInterrupt(digitalPinToInterrupt(SENSOR_INTERRUPT_PIN), handle_sensor_interrupt, FALLING);
    }

    xTaskCreatePinnedToCore(TaskNetwork, "Network Task", 8192, NULL, 1, NULL, 1);
    
    log_message(LOG_LEVEL_INFO, "System", "Setup complete. Awaiting commands from web UI.");
}

void loop() {
    uint32_t current_time = millis();
    static uint32_t last_health_check = 0;
    static uint32_t zero_reading_count = 0;
    static uint32_t last_zero_log = 0;

    if (current_time - last_health_check > 5000) {
        if (!check_sensor_health()) {
            log_message(LOG_LEVEL_ERROR, "Loop", "Sensor health check failed - attempting recovery");
            if (init_sensor()) {
                calibration_init();
                log_message(LOG_LEVEL_INFO, "Loop", "Sensor recovery successful");
                zero_reading_count = 0;
            }
        }
        last_health_check = current_time;
    }

    if (g_is_scanning && (current_time - last_sensor_read_time >= READING_INTERVAL_MS)) {
        last_sensor_read_time = current_time;
        
        xyz_color_s measured_xyz = {(float)tcs3430_sensor.getXData(), (float)tcs3430_sensor.getYData(), (float)tcs3430_sensor.getZData()};
        uint16_t ir1_raw = tcs3430_sensor.getIR1Data();
        uint16_t ir2_raw = tcs3430_sensor.getIR2Data();
        bool data_invalid = false;
        char diagnostic_msg[100];

        if (isnan(measured_xyz.x) || isnan(measured_xyz.y) || isnan(measured_xyz.z)) {
            snprintf(diagnostic_msg, sizeof(diagnostic_msg), "NaN detected - X:%s, Y:%s, Z:%s",
                     isnan(measured_xyz.x) ? "NaN" : "OK", isnan(measured_xyz.y) ? "NaN" : "OK", isnan(measured_xyz.z) ? "NaN" : "OK");
            log_message(LOG_LEVEL_ERROR, "Sensor", diagnostic_msg);
            data_invalid = true;
        }
        if (measured_xyz.x <= 0.0f && measured_xyz.y <= 0.0f && measured_xyz.z <= 0.0f) {
            zero_reading_count++;
            if (millis() - last_zero_log > 10000) {
                snprintf(diagnostic_msg, sizeof(diagnostic_msg), "Zero values (%d times) - X:%.1f, Y:%.1f, Z:%.1f, IR1:%d, IR2:%d",
                         zero_reading_count, measured_xyz.x, measured_xyz.y, measured_xyz.z, ir1_raw, ir2_raw);
                log_message(LOG_LEVEL_INFO, "Sensor", diagnostic_msg);
                uint8_t sensor_status = tcs3430_sensor.getDeviceStatus();
                snprintf(diagnostic_msg, sizeof(diagnostic_msg), "Sensor status: 0x%02X (PON:%d, AEN:%d, ASAT:%d, AINT:%d)",
                         sensor_status, (sensor_status & 0x01), (sensor_status & 0x02) >> 1, (sensor_status & 0x08) >> 3, (sensor_status & 0x10) >> 4);
                log_message(LOG_LEVEL_INFO, "Sensor", diagnostic_msg);
                last_zero_log = millis();
            }
            if (zero_reading_count > 20 && tcs3430_sensor.getDeviceStatus() == 0xFF) {
                log_message(LOG_LEVEL_ERROR, "Sensor", "Too many zero readings - triggering recovery");
                if (init_sensor()) {
                    calibration_init();
                    zero_reading_count = 0;
                }
            }
            if (g_web_data.data_ready) {
                g_web_data.data_ready = false;
                strncpy(g_web_data.matched_name, "No Object", sizeof(g_web_data.matched_name)-1);
                g_web_data.matched_name[sizeof(g_web_data.matched_name)-1] = '\0';
                broadcast_data_update();
            }
            return;
        } else {
            zero_reading_count = 0;
        }
        if (data_invalid) {
            uint8_t sensor_status = tcs3430_sensor.getDeviceStatus();
            snprintf(diagnostic_msg, sizeof(diagnostic_msg), "Sensor status: 0x%02X (PON:%d, AEN:%d, ASAT:%d, AINT:%d)",
                     sensor_status, (sensor_status & 0x01), (sensor_status & 0x02) >> 1, (sensor_status & 0x08) >> 3, (sensor_status & 0x10) >> 4);
            log_message(LOG_LEVEL_ERROR, "Sensor", diagnostic_msg);
            strncpy(g_web_data.matched_name, "Invalid Data", sizeof(g_web_data.matched_name)-1);
            g_web_data.matched_name[sizeof(g_web_data.matched_name)-1] = '\0';
            g_web_data.data_ready = false;
            broadcast_data_update();
            return;
        }

        apply_ir_compensation(measured_xyz, ir1_raw, ir2_raw, current_app_calibration.k_ir_compensation_factor);
        if (measured_xyz.x <= 0.0f || measured_xyz.y <= 0.0f || measured_xyz.z <= 0.0f || isnan(measured_xyz.x) || isnan(measured_xyz.y) || isnan(measured_xyz.z)) {
            log_message(LOG_LEVEL_WARN, "Sensor", "Invalid XYZ data after IR compensation.");
            strncpy(g_web_data.matched_name, "Invalid Data", sizeof(g_web_data.matched_name)-1);
            g_web_data.matched_name[sizeof(g_web_data.matched_name)-1] = '\0';
            g_web_data.data_ready = false;
            broadcast_data_update();
            return;
        }

        lab_color_s measured_lab;
        xyz_to_lab(measured_xyz, measured_lab, D65_WHITEPOINT);
        if (isnan(measured_lab.l) || isnan(measured_lab.a) || isnan(measured_lab.b)) {
            log_message(LOG_LEVEL_WARN, "Sensor", "Invalid Lab data.");
            strncpy(g_web_data.matched_name, "Invalid Data", sizeof(g_web_data.matched_name)-1);
            g_web_data.matched_name[sizeof(g_web_data.matched_name)-1] = '\0';
            g_web_data.data_ready = false;
            broadcast_data_update();
            return;
        }

        float delta_e_val = -1.0f;
        int matched_idx = find_closest_color_in_db(measured_lab, delta_e_val);

        rgb_color_s output_rgb = xyz_to_srgb_enhanced(measured_xyz, current_app_calibration.srgb_output_norm_factor);
        if (output_rgb.r > 255 || output_rgb.g > 255 || output_rgb.b > 255) {
            log_message(LOG_LEVEL_WARN, "Sensor", "Invalid RGB values, clamping.");
            output_rgb.r = constrain(output_rgb.r, 0, 255);
            output_rgb.g = constrain(output_rgb.g, 0, 255);
            output_rgb.b = constrain(output_rgb.b, 0, 255);
        }
        
        g_web_data.measured_r = output_rgb.r; 
        g_web_data.measured_g = output_rgb.g; 
        g_web_data.measured_b = output_rgb.b;
        g_web_data.delta_e = delta_e_val;
        
        const char* confidence_str = (delta_e_val <= DELTA_E_THRESHOLD) ? "High" : (delta_e_val <= DELTA_E_THRESHOLD * 2) ? "Medium" : "Low";
        strncpy(g_web_data.confidence, confidence_str, sizeof(g_web_data.confidence) - 1);
        g_web_data.confidence[sizeof(g_web_data.confidence) - 1] = '\0';
        
        if (matched_idx != -1) {
            strncpy(g_web_data.matched_name, color_database[matched_idx].name, MAX_COLOR_NAME_LEN - 1);
            g_web_data.matched_name[MAX_COLOR_NAME_LEN - 1] = '\0';
            g_web_data.matched_r = color_database[matched_idx].rgb_ref.r;
            g_web_data.matched_g = color_database[matched_idx].rgb_ref.g;
            g_web_data.matched_b = color_database[matched_idx].rgb_ref.b;
        } else {
            strncpy(g_web_data.matched_name, "Unknown", sizeof(g_web_data.matched_name) - 1);
            g_web_data.matched_name[sizeof(g_web_data.matched_name) - 1] = '\0';
            g_web_data.matched_r = 0; g_web_data.matched_g = 0; g_web_data.matched_b = 0;
        }
        g_web_data.data_ready = true;
        g_web_data.avg_x = measured_xyz.x;
        g_web_data.avg_y = measured_xyz.y;
        g_web_data.avg_z = measured_xyz.z;
        g_web_data.avg_l = measured_lab.l;
        g_web_data.avg_a = measured_lab.a;
        g_web_data.avg_b = measured_lab.b;
        g_web_data.avg_ir1 = ir1_raw;
        g_web_data.avg_ir2 = ir2_raw;
        broadcast_data_update();

        if (!wifiConnected && g_is_scanning) {
            snprintf(logBuffer, sizeof(logBuffer), "RGB: (%d, %d, %d), Match: %s, DeltaE: %.2f, Confidence: %s",
                     g_web_data.measured_r, g_web_data.measured_g, g_web_data.measured_b,
                     g_web_data.matched_name, g_web_data.delta_e, g_web_data.confidence);
            log_message(LOG_LEVEL_INFO, "Sensor", logBuffer);
        }
    }
    
    vTaskDelay(pdMS_TO_TICKS(50));
}