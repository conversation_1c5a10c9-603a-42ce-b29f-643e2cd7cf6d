/*
 * ESP32-S3 Color Matching Device - Working Version with WiFi
 * 
 * A working version that compiles successfully and provides core functionality
 * with proper WiFi connectivity to "Wifi 6" network.
 * 
 * Features:
 * - TCS3430 color sensor reading with I2C communication
 * - WiFi connectivity using ESP32 built-in WiFi
 * - Simple color matching with built-in color database
 * - LED status indicators
 * - Serial output for debugging and data display
 * - Basic error handling and sensor health checks
 * 
 * Hardware Configuration:
 * - I2C: SDA=8, SCL=9
 * - Status LED: GPIO 5
 * - Illumination LED: GPIO 4  
 * - Sensor Interrupt: GPIO 21
 * 
 * Network Configuration:
 * - SSID: "Wifi 6"
 * - Password: "Scrofani1985"
 * - Static IP: *************
 */

#include <Arduino.h>
#include <Wire.h>
#include <WiFi.h>
#include "DFRobot_TCS3430.h"  // Use local library
#include <ArduinoJson.h>
#include <math.h>
#include <WebServer.h>
#include <LittleFS.h>
#include <Adafruit_NeoPixel.h>
#include <Preferences.h>

// ------------------------------------------------------------------------------------
// Configuration Constants
// ------------------------------------------------------------------------------------
#define I2C_SDA_PIN          8
#define I2C_SCL_PIN          9
#define ILLUMINATION_LED_PIN 5  // White LED for scanning illumination
#define RGB_LED_PIN          18 // ProS3 onboard RGB LED (WS2812)
#define SENSOR_INTERRUPT_PIN 21
#define READING_INTERVAL_MS  500
#define MAX_COLOR_NAME_LEN   35
#define MAX_DULUX_COLORS     1500

// Network Configuration
const char* WIFI_SSID = "Wifi 6";
const char* WIFI_PASSWORD = "Scrofani1985";
const IPAddress STATIC_IP(192, 168, 0, 201);
const IPAddress GATEWAY(192, 168, 0, 1);
const IPAddress SUBNET(255, 255, 255, 0);

// ------------------------------------------------------------------------------------
// Global Variables
// ------------------------------------------------------------------------------------
DFRobot_TCS3430 colorSensor;
Adafruit_NeoPixel rgbLED(1, RGB_LED_PIN, NEO_GRB + NEO_KHZ800); // ProS3 uses NEO_GRB format
unsigned long lastReading = 0;
unsigned long lastRGBUpdate = 0;
int rgbColorWheel = 0;
bool illuminationState = false;
bool manualLEDToggle = false;  // Manual LED toggle state (independent of scanning)
bool sensorInitialized = false;
bool wifiConnected = false;

// ------------------------------------------------------------------------------------
// Settings Structure for Precision Color Measurement
// ------------------------------------------------------------------------------------
struct ColorSettings {
    // Sensor Configuration
    uint8_t integrationTime;    // Integration time (0x00-0xFF, default 0x40 = ~100ms)
    uint8_t alsGain;           // ALS Gain (0=1x, 1=4x, 2=16x, 3=64x, default 1=4x)

    // Color Processing
    float irCompensation;      // IR compensation factor (default 0.03)
    uint16_t srgbNormalization; // sRGB normalization factor (default 15000)
    bool adaptiveScaling;      // Adaptive scaling enabled (default true)

    // Calibration flags
    bool isCalibrated;         // Overall calibration status
    bool hasWhiteBalance;      // White balance calibration status
    bool hasDarkCalibration;   // Dark calibration status
};

// Default settings for optimal color measurement
const ColorSettings DEFAULT_SETTINGS = {
    .integrationTime = 0x40,    // ~100ms for good accuracy
    .alsGain = 1,              // 4x gain for balanced sensitivity
    .irCompensation = 0.03f,   // Minimal IR compensation
    .srgbNormalization = 15000, // Standard normalization
    .adaptiveScaling = true,   // Enable adaptive scaling
    .isCalibrated = false,
    .hasWhiteBalance = false,
    .hasDarkCalibration = false
};

// Current settings (loaded from EEPROM or defaults)
ColorSettings currentSettings = DEFAULT_SETTINGS;

// Preferences object for EEPROM storage
Preferences preferences;

// ------------------------------------------------------------------------------------
// Precision Color Data Structure
// ------------------------------------------------------------------------------------
struct PrecisionColorData {
    uint16_t x, y, z;           // Raw XYZ tristimulus values
    uint16_t ir1, ir2;          // IR values
    float calibratedX, calibratedY, calibratedZ; // Calibrated XYZ values
    uint8_t r, g, b;            // RGB values (0-255)
    char hexColor[8];           // Hex color string
    float confidence;           // Measurement confidence (0-100%)
    bool isSaturated;           // Saturation warning
    bool isCalibrated;          // Using calibrated values
};

PrecisionColorData currentColor = {0};

// ------------------------------------------------------------------------------------
// Sample Storage Structure for Dual-Display System
// ------------------------------------------------------------------------------------
#define MAX_STORED_SAMPLES 30

struct ColorSample {
    // Frozen sample data
    uint8_t r, g, b;            // RGB values (0-255)
    char hexColor[8];           // Hex color string
    char duluxName[MAX_COLOR_NAME_LEN]; // Matched Dulux color name
    uint8_t duluxR, duluxG, duluxB;     // Dulux match RGB
    float deltaE;               // Color difference

    // Raw sensor data for reference
    uint16_t rawX, rawY, rawZ;  // Raw XYZ values
    uint16_t rawIR1, rawIR2;    // IR values
    float confidence;           // Measurement confidence

    // Metadata
    unsigned long timestamp;    // When sample was captured
    bool isValid;              // Sample slot is occupied
};

// Circular buffer for stored samples
ColorSample storedSamples[MAX_STORED_SAMPLES];
int currentSampleIndex = 0;    // Next slot to write to
int totalSamples = 0;          // Total samples stored (max 30)

// Current frozen sample (displayed on right side)
ColorSample frozenSample = {0};
bool hasFrozenSample = false;

// Dulux color database structure
struct ReferenceColor {
    char name[MAX_COLOR_NAME_LEN];
    uint8_t r, g, b;
};

ReferenceColor colorDatabase[MAX_DULUX_COLORS];
int COLOR_DATABASE_SIZE = 0;

// Fallback colors if dulux.json fails to load
const ReferenceColor fallbackColors[] = {
    {"White", 255, 255, 255},
    {"Black", 0, 0, 0},
    {"Red", 255, 0, 0},
    {"Green", 0, 255, 0},
    {"Blue", 0, 0, 255},
    {"Yellow", 255, 255, 0},
    {"Cyan", 0, 255, 255},
    {"Magenta", 255, 0, 255},
    {"Orange", 255, 165, 0},
    {"Purple", 128, 0, 128},
    {"Brown", 165, 42, 42},
    {"Gray", 128, 128, 128},
    {"Pink", 255, 192, 203},
    {"Lime", 0, 255, 0},
    {"Navy", 0, 0, 128}
};

const int FALLBACK_COLOR_COUNT = sizeof(fallbackColors) / sizeof(fallbackColors[0]);

// ------------------------------------------------------------------------------------
// Web Server Variables
// ------------------------------------------------------------------------------------
WebServer server(80);
bool isScanning = false;
unsigned long lastWebUpdate = 0;

// ------------------------------------------------------------------------------------
// Sample Storage Management Functions
// ------------------------------------------------------------------------------------
void saveSamplesToEEPROM() {
    preferences.begin("colorSamples", false);

    // Save sample metadata
    preferences.putInt("totalSamples", totalSamples);
    preferences.putInt("currentIndex", currentSampleIndex);

    // Save each sample
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        String key = "sample_" + String(i);
        if (storedSamples[i].isValid) {
            preferences.putBytes(key.c_str(), &storedSamples[i], sizeof(ColorSample));
        } else {
            preferences.remove(key.c_str());
        }
    }

    preferences.end();
    Serial.printf("Saved %d samples to EEPROM\n", totalSamples);
}

void loadSamplesFromEEPROM() {
    preferences.begin("colorSamples", true);

    // Load sample metadata
    totalSamples = preferences.getInt("totalSamples", 0);
    currentSampleIndex = preferences.getInt("currentIndex", 0);

    // Initialize all samples as invalid
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        storedSamples[i].isValid = false;
    }

    // Load each sample
    int loadedCount = 0;
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        String key = "sample_" + String(i);
        size_t dataSize = preferences.getBytesLength(key.c_str());
        if (dataSize == sizeof(ColorSample)) {
            preferences.getBytes(key.c_str(), &storedSamples[i], sizeof(ColorSample));
            if (storedSamples[i].isValid) {
                loadedCount++;
            }
        }
    }

    preferences.end();
    Serial.printf("Loaded %d samples from EEPROM\n", loadedCount);
}

void addSampleToStorage(const ColorSample& sample) {
    // Add sample to circular buffer
    storedSamples[currentSampleIndex] = sample;
    storedSamples[currentSampleIndex].isValid = true;
    storedSamples[currentSampleIndex].timestamp = millis();

    // Update indices
    currentSampleIndex = (currentSampleIndex + 1) % MAX_STORED_SAMPLES;
    if (totalSamples < MAX_STORED_SAMPLES) {
        totalSamples++;
    }

    // Save to EEPROM
    saveSamplesToEEPROM();

    Serial.printf("Sample added to storage. Total samples: %d\n", totalSamples);
}

void clearAllSamples() {
    // Clear memory
    for (int i = 0; i < MAX_STORED_SAMPLES; i++) {
        storedSamples[i].isValid = false;
    }
    totalSamples = 0;
    currentSampleIndex = 0;
    hasFrozenSample = false;

    // Clear EEPROM
    preferences.begin("colorSamples", false);
    preferences.clear();
    preferences.end();

    Serial.println("All samples cleared from storage and EEPROM");
}



// ------------------------------------------------------------------------------------
// Settings Management Functions
// ------------------------------------------------------------------------------------
// Forward declaration
void applySensorSettings();
void saveSettings() {
    preferences.begin("colorSettings", false);
    preferences.putUChar("integrationTime", currentSettings.integrationTime);
    preferences.putUChar("alsGain", currentSettings.alsGain);
    preferences.putFloat("irCompensation", currentSettings.irCompensation);
    preferences.putUShort("srgbNorm", currentSettings.srgbNormalization);
    preferences.putBool("adaptiveScaling", currentSettings.adaptiveScaling);
    preferences.putBool("isCalibrated", currentSettings.isCalibrated);
    preferences.putBool("hasWhiteBalance", currentSettings.hasWhiteBalance);
    preferences.putBool("hasDarkCalib", currentSettings.hasDarkCalibration);
    preferences.end();
    Serial.println("Settings saved to EEPROM");
}

void loadSettings() {
    preferences.begin("colorSettings", true);

    // Load settings with defaults if not found
    currentSettings.integrationTime = preferences.getUChar("integrationTime", DEFAULT_SETTINGS.integrationTime);
    currentSettings.alsGain = preferences.getUChar("alsGain", DEFAULT_SETTINGS.alsGain);
    currentSettings.irCompensation = preferences.getFloat("irCompensation", DEFAULT_SETTINGS.irCompensation);
    currentSettings.srgbNormalization = preferences.getUShort("srgbNorm", DEFAULT_SETTINGS.srgbNormalization);
    currentSettings.adaptiveScaling = preferences.getBool("adaptiveScaling", DEFAULT_SETTINGS.adaptiveScaling);
    currentSettings.isCalibrated = preferences.getBool("isCalibrated", DEFAULT_SETTINGS.isCalibrated);
    currentSettings.hasWhiteBalance = preferences.getBool("hasWhiteBalance", DEFAULT_SETTINGS.hasWhiteBalance);
    currentSettings.hasDarkCalibration = preferences.getBool("hasDarkCalib", DEFAULT_SETTINGS.hasDarkCalibration);

    preferences.end();

    Serial.println("Settings loaded from EEPROM:");
    Serial.printf("  Integration Time: 0x%02X (~%.1fms)\n", currentSettings.integrationTime, (currentSettings.integrationTime + 1) * 2.78f);
    Serial.printf("  ALS Gain: %dx\n", (1 << (currentSettings.alsGain * 2)));
    Serial.printf("  IR Compensation: %.3f\n", currentSettings.irCompensation);
    Serial.printf("  sRGB Normalization: %d\n", currentSettings.srgbNormalization);
    Serial.printf("  Adaptive Scaling: %s\n", currentSettings.adaptiveScaling ? "Enabled" : "Disabled");
}

void resetToDefaults() {
    currentSettings = DEFAULT_SETTINGS;
    saveSettings();
    applySensorSettings();
    Serial.println("Settings reset to defaults");
}

void applySensorSettings() {
    colorSensor.setIntegrationTime(currentSettings.integrationTime);
    colorSensor.setALSGain(currentSettings.alsGain);
    Serial.printf("Applied sensor settings: Integration=0x%02X, Gain=%dx\n",
                  currentSettings.integrationTime, (1 << (currentSettings.alsGain * 2)));
}

// ------------------------------------------------------------------------------------
// Dulux Color Database (for reference display only)
// ------------------------------------------------------------------------------------
bool loadDuluxDatabase() {
    if (!LittleFS.exists("/dulux.json")) {
        Serial.println("dulux.json not found, using built-in colors");
        return false;
    }

    File file = LittleFS.open("/dulux.json", "r");
    if (!file) {
        Serial.println("Failed to open dulux.json");
        return false;
    }

    // Check file size and available memory
    size_t fileSize = file.size();
    size_t freeHeap = ESP.getFreeHeap();
    size_t freePsram = ESP.getFreePsram();

    Serial.printf("Dulux JSON file size: %d bytes\n", fileSize);
    Serial.printf("Free heap: %d bytes, Free PSRAM: %d bytes\n", freeHeap, freePsram);

    // If file is too large for available memory, skip loading
    if (fileSize > (freeHeap / 2)) {
        Serial.println("JSON file too large for available heap memory");
        Serial.println("Skipping Dulux database loading to prevent crashes");
        file.close();
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    Serial.printf("Free heap after JSON parsing: %d bytes\n", ESP.getFreeHeap());

    if (error) {
        Serial.print("Failed to parse dulux.json: ");
        Serial.println(error.c_str());
        Serial.println("This is likely due to the JSON file being too large for available memory");
        Serial.println("Using fallback color database instead");
        return false;
    }

    // Clear existing database
    COLOR_DATABASE_SIZE = 0;

    JsonArray colors = doc.as<JsonArray>();
    for (JsonVariant colorVar : colors) {
        if (COLOR_DATABASE_SIZE >= MAX_DULUX_COLORS) break;

        JsonObject color = colorVar.as<JsonObject>();
        if (!color["name"].is<const char*>() || !color["r"].is<int>() ||
            !color["g"].is<int>() || !color["b"].is<int>()) continue;

        strncpy(colorDatabase[COLOR_DATABASE_SIZE].name, color["name"], MAX_COLOR_NAME_LEN - 1);
        colorDatabase[COLOR_DATABASE_SIZE].name[MAX_COLOR_NAME_LEN - 1] = '\0';
        colorDatabase[COLOR_DATABASE_SIZE].r = color["r"];
        colorDatabase[COLOR_DATABASE_SIZE].g = color["g"];
        colorDatabase[COLOR_DATABASE_SIZE].b = color["b"];
        COLOR_DATABASE_SIZE++;
    }

    Serial.printf("Loaded %d colors from dulux.json\n", COLOR_DATABASE_SIZE);
    return true;
}

// ------------------------------------------------------------------------------------
// Helper Functions
// Color wheel function for smooth rainbow cycling (matching MicroPython pros3.rgb_color_wheel)
uint32_t colorWheel(byte wheelPos) {
    // Normalize to 0-255 range
    wheelPos = wheelPos % 256;

    if (wheelPos < 85) {
        // Red to Green transition
        return rgbLED.Color(255 - wheelPos * 3, wheelPos * 3, 0);
    } else if (wheelPos < 170) {
        // Green to Blue transition
        wheelPos -= 85;
        return rgbLED.Color(0, 255 - wheelPos * 3, wheelPos * 3);
    } else {
        // Blue to Red transition
        wheelPos -= 170;
        return rgbLED.Color(wheelPos * 3, 0, 255 - wheelPos * 3);
    }
}

void updateRGBStatusLED() {
    // Update RGB LED every 15ms for smooth color cycling
    if (millis() - lastRGBUpdate > 15) {
        // Cycle through rainbow colors
        uint32_t color = colorWheel(rgbColorWheel);
        rgbLED.setPixelColor(0, color);
        rgbLED.show();

        // Debug output every 5 seconds
        static unsigned long lastDebug = 0;
        if (millis() - lastDebug > 5000) {
            Serial.printf("ProS3 RGB LED: wheel=%d, color=0x%06X, pin=%d\n",
                         rgbColorWheel, color, RGB_LED_PIN);
            lastDebug = millis();
        }

        rgbColorWheel++;
        if (rgbColorWheel >= 256) {
            rgbColorWheel = 0;
        }
        lastRGBUpdate = millis();
    }
}

void setIlluminationLED(bool state) {
    // Only update and log if state actually changes
    if (illuminationState != state) {
        illuminationState = state;
        digitalWrite(ILLUMINATION_LED_PIN, state ? HIGH : LOW);
        Serial.printf("Illumination LED set to: %s\n", state ? "ON" : "OFF");
    }
}

// Manual LED toggle function (independent of scanning)
void toggleManualLED() {
    manualLEDToggle = !manualLEDToggle;
    setIlluminationLED(manualLEDToggle);
    Serial.printf("Manual LED toggle: %s\n", manualLEDToggle ? "ON" : "OFF");
}

// Calculate measurement confidence based on sensor conditions
float calculateMeasurementConfidence() {
    float confidence = 100.0f;

    // Reduce confidence if saturated
    if (currentColor.isSaturated) {
        confidence -= 30.0f;
    }

    // Reduce confidence for very low light levels
    uint16_t maxChannel = max(currentColor.x, max(currentColor.y, currentColor.z));
    if (maxChannel < 100) {
        confidence -= 40.0f;
    } else if (maxChannel < 500) {
        confidence -= 20.0f;
    }

    // Boost confidence if calibrated
    if (currentColor.isCalibrated) {
        confidence += 10.0f;
    }

    return max(0.0f, min(100.0f, confidence));
}

// Apply advanced color processing with current settings
void processColorMeasurement() {
    // Apply IR compensation if enabled
    if (currentSettings.irCompensation > 0) {
        float irFactor = currentSettings.irCompensation;
        currentColor.x = max(0, (int)(currentColor.x - currentColor.ir1 * irFactor));
        currentColor.y = max(0, (int)(currentColor.y - currentColor.ir1 * irFactor));
        currentColor.z = max(0, (int)(currentColor.z - currentColor.ir1 * irFactor));
    }

    // Apply adaptive scaling if enabled
    if (currentSettings.adaptiveScaling) {
        uint16_t maxChannel = max(currentColor.x, max(currentColor.y, currentColor.z));
        if (maxChannel > 0 && maxChannel < currentSettings.srgbNormalization / 4) {
            // Boost low signals
            float scaleFactor = (float)currentSettings.srgbNormalization / (maxChannel * 4);
            scaleFactor = min(scaleFactor, 4.0f); // Limit boost to 4x
            currentColor.x = min(65535, (int)(currentColor.x * scaleFactor));
            currentColor.y = min(65535, (int)(currentColor.y * scaleFactor));
            currentColor.z = min(65535, (int)(currentColor.z * scaleFactor));
        }
    }
}

// Convert RGB to hex string
void rgbToHex(uint8_t r, uint8_t g, uint8_t b, char* hexStr) {
    sprintf(hexStr, "#%02X%02X%02X", r, g, b);
}

// ------------------------------------------------------------------------------------
// Sensor Functions
// ------------------------------------------------------------------------------------
bool initializeSensor() {
    Serial.println("Initializing TCS3430 precision color sensor...");

    if (!colorSensor.begin()) {
        Serial.println("ERROR: Failed to initialize TCS3430 sensor!");
        return false;
    }

    // Load settings and apply to sensor
    loadSettings();
    applySensorSettings();

    Serial.println("TCS3430 precision color sensor initialized successfully");
    return true;
}

bool readPrecisionColorSensor() {
    if (!sensorInitialized) {
        Serial.println("ERROR: Sensor not initialized");
        return false;
    }

    // Read raw XYZ and IR values
    currentColor.x = colorSensor.getXData();
    currentColor.y = colorSensor.getYData();
    currentColor.z = colorSensor.getZData();
    currentColor.ir1 = colorSensor.getIR1Data();
    currentColor.ir2 = colorSensor.getIR2Data();

    // Check for saturation
    currentColor.isSaturated = colorSensor.isSaturated();

    // Apply advanced color processing
    processColorMeasurement();

    // Get calibrated XYZ values if calibration is available
    currentColor.isCalibrated = colorSensor.getCalibratedXYZ(&currentColor.calibratedX,
                                                             &currentColor.calibratedY,
                                                             &currentColor.calibratedZ);

    if (currentColor.isCalibrated) {
        // Use proper XYZ to RGB conversion with calibrated values
        colorSensor.convertXYZtoRGB(currentColor.calibratedX, currentColor.calibratedY, currentColor.calibratedZ,
                                   (uint8_t*)&currentColor.r,
                                   (uint8_t*)&currentColor.g,
                                   (uint8_t*)&currentColor.b);
    } else {
        // Use processed raw values with normalization
        float X = (float)currentColor.x / currentSettings.srgbNormalization;
        float Y = (float)currentColor.y / currentSettings.srgbNormalization;
        float Z = (float)currentColor.z / currentSettings.srgbNormalization;

        colorSensor.convertXYZtoRGB(X, Y, Z,
                                   (uint8_t*)&currentColor.r,
                                   (uint8_t*)&currentColor.g,
                                   (uint8_t*)&currentColor.b);
    }

    // Generate hex color string
    rgbToHex(currentColor.r, currentColor.g, currentColor.b, currentColor.hexColor);

    // Calculate measurement confidence
    currentColor.confidence = calculateMeasurementConfidence();

    return true;
}

// ------------------------------------------------------------------------------------
// Web Server Functions
// ------------------------------------------------------------------------------------
void handleRoot() {
    File file = LittleFS.open("/index.html", "r");
    if (file) {
        server.streamFile(file, "text/html");
        file.close();
    } else {
        server.send(404, "text/plain", "index.html not found");
    }
}

void handleStyleCSS() {
    File file = LittleFS.open("/style.css", "r");
    if (file) {
        server.streamFile(file, "text/css");
        file.close();
    } else {
        server.send(404, "text/plain", "style.css not found");
    }
}

void handleFullData() {
    JsonDocument doc;
    doc["data_ready"] = true;

    // Main color measurement data (for compatibility with existing UI)
    doc["measured_r"] = currentColor.r;
    doc["measured_g"] = currentColor.g;
    doc["measured_b"] = currentColor.b;
    doc["hex_color"] = currentColor.hexColor;

    // Enhanced precision measurement data
    doc["raw_x"] = currentColor.x;
    doc["raw_y"] = currentColor.y;
    doc["raw_z"] = currentColor.z;
    doc["ir1"] = currentColor.ir1;
    doc["ir2"] = currentColor.ir2;

    // Calibrated XYZ values (if available)
    if (currentColor.isCalibrated) {
        doc["calibrated_x"] = currentColor.calibratedX;
        doc["calibrated_y"] = currentColor.calibratedY;
        doc["calibrated_z"] = currentColor.calibratedZ;
        doc["using_calibration"] = true;
    } else {
        doc["calibrated_x"] = nullptr;
        doc["calibrated_y"] = nullptr;
        doc["calibrated_z"] = nullptr;
        doc["using_calibration"] = false;
    }

    // Measurement quality and confidence
    doc["confidence"] = currentColor.confidence;
    doc["confidence_text"] = currentColor.confidence >= 80 ? "Excellent" :
                            currentColor.confidence >= 60 ? "Good" :
                            currentColor.confidence >= 40 ? "Fair" : "Poor";
    doc["is_saturated"] = currentColor.isSaturated;
    doc["saturation_warning"] = currentColor.isSaturated ? "Sensor saturated - reduce gain or lighting" : "";

    // Current sensor settings for display
    doc["current_integration_time"] = currentSettings.integrationTime;
    doc["current_integration_ms"] = (currentSettings.integrationTime + 1) * 2.78f;
    doc["current_gain"] = currentSettings.alsGain;
    doc["current_gain_text"] = String((1 << (currentSettings.alsGain * 2))) + "x";
    doc["current_ir_compensation"] = currentSettings.irCompensation;
    doc["current_normalization"] = currentSettings.srgbNormalization;
    doc["current_adaptive_scaling"] = currentSettings.adaptiveScaling;

    // Calibration status
    doc["has_white_balance"] = currentSettings.hasWhiteBalance;
    doc["has_dark_calibration"] = currentSettings.hasDarkCalibration;
    doc["is_fully_calibrated"] = currentSettings.isCalibrated;

    // System status
    doc["is_scanning"] = isScanning;
    doc["led_state"] = illuminationState;
    doc["manual_led_toggle"] = manualLEDToggle;

    // Frozen sample data (right side display)
    if (hasFrozenSample) {
        doc["frozen_sample"] = true;
        doc["matched_name"] = frozenSample.duluxName;
        doc["matched_r"] = frozenSample.duluxR;
        doc["matched_g"] = frozenSample.duluxG;
        doc["matched_b"] = frozenSample.duluxB;
        doc["delta_e"] = frozenSample.deltaE;
        doc["frozen_hex"] = frozenSample.hexColor;
        doc["frozen_confidence"] = frozenSample.confidence;
    } else {
        doc["frozen_sample"] = false;
        doc["matched_name"] = "No Sample Frozen";
        doc["matched_r"] = currentColor.r;
        doc["matched_g"] = currentColor.g;
        doc["matched_b"] = currentColor.b;
        doc["delta_e"] = 0;
    }

    // Sample storage info
    doc["total_stored_samples"] = totalSamples;
    doc["max_samples"] = MAX_STORED_SAMPLES;

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleStartScan() {
    isScanning = true;
    Serial.println("=== SCAN STARTED ===");
    server.send(200, "text/plain", "Scan started");
}

void handleStopScan() {
    isScanning = false;
    Serial.println("=== SCAN STOPPED ===");

    // Automatically save current color sample to storage
    if (sensorInitialized) {
        // Create a sample from current color data
        ColorSample newSample;
        newSample.r = currentColor.r;
        newSample.g = currentColor.g;
        newSample.b = currentColor.b;
        strncpy(newSample.hexColor, currentColor.hexColor, sizeof(newSample.hexColor));

        // Find closest Dulux match for the sample
        float minDeltaE = 999.0f;
        int bestMatch = -1;
        for (int i = 0; i < COLOR_DATABASE_SIZE; i++) {
            // Simple RGB distance calculation
            float deltaR = (float)currentColor.r - colorDatabase[i].r;
            float deltaG = (float)currentColor.g - colorDatabase[i].g;
            float deltaB = (float)currentColor.b - colorDatabase[i].b;
            float distance = sqrt(deltaR*deltaR + deltaG*deltaG + deltaB*deltaB);

            if (distance < minDeltaE) {
                minDeltaE = distance;
                bestMatch = i;
            }
        }

        // Store Dulux match info
        if (bestMatch >= 0) {
            strncpy(newSample.duluxName, colorDatabase[bestMatch].name, sizeof(newSample.duluxName));
            newSample.duluxR = colorDatabase[bestMatch].r;
            newSample.duluxG = colorDatabase[bestMatch].g;
            newSample.duluxB = colorDatabase[bestMatch].b;
            newSample.deltaE = minDeltaE;
        } else {
            strncpy(newSample.duluxName, "No Match", sizeof(newSample.duluxName));
            newSample.duluxR = currentColor.r;
            newSample.duluxG = currentColor.g;
            newSample.duluxB = currentColor.b;
            newSample.deltaE = 999.0f;
        }

        // Store raw sensor data
        newSample.rawX = currentColor.x;
        newSample.rawY = currentColor.y;
        newSample.rawZ = currentColor.z;
        newSample.rawIR1 = currentColor.ir1;
        newSample.rawIR2 = currentColor.ir2;
        newSample.confidence = currentColor.confidence;

        // Add to storage (automatically handles circular buffer and EEPROM)
        addSampleToStorage(newSample);

        // Set as frozen sample for display
        frozenSample = newSample;
        hasFrozenSample = true;

        Serial.printf("Sample saved: RGB(%d,%d,%d) -> %s (ΔE=%.1f)\n",
                     newSample.r, newSample.g, newSample.b, newSample.duluxName, newSample.deltaE);
    }

    server.send(200, "text/plain", "Scan stopped and sample saved");
}

void handleToggleLED() {
    // Toggle manual LED state (independent of scanning)
    manualLEDToggle = !manualLEDToggle;

    // Apply the manual toggle state
    setIlluminationLED(manualLEDToggle);

    Serial.printf("Manual LED toggle: %s (independent of scanning)\n", manualLEDToggle ? "ON" : "OFF");

    JsonDocument doc;
    doc["led_state"] = manualLEDToggle;
    doc["manual_toggle"] = manualLEDToggle;
    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

// Add missing API endpoints that your UI expects
void handleWhiteBalance() {
    Serial.println("=== WHITE BALANCE CALIBRATION ===");
    Serial.println("Place a white reference object (white paper) under the sensor and ensure good lighting");

    // Turn on LED for calibration
    setIlluminationLED(true);
    delay(500); // Allow LED to stabilize

    // Take multiple readings for better accuracy
    uint16_t whiteX = 0, whiteY = 0, whiteZ = 0;
    int validReadings = 0;

    for (int i = 0; i < 10; i++) {
        delay(100);
        uint16_t x = colorSensor.getXData();
        uint16_t y = colorSensor.getYData();
        uint16_t z = colorSensor.getZData();

        // Only use readings that are reasonably bright (white should be bright)
        if (x > 200 && y > 200 && z > 50) {
            whiteX += x;
            whiteY += y;
            whiteZ += z;
            validReadings++;
        }

        Serial.printf("White sample %d: X=%d, Y=%d, Z=%d\n", i+1, x, y, z);
    }

    if (validReadings >= 5) {
        // Calculate average white reference
        whiteX /= validReadings;
        whiteY /= validReadings;
        whiteZ /= validReadings;

        // Perform proper white balance calibration
        bool success = colorSensor.performWhiteBalanceCalibration(10);

        if (success) {
            currentSettings.hasWhiteBalance = true;
            currentSettings.isCalibrated = currentSettings.hasWhiteBalance && currentSettings.hasDarkCalibration;
            saveSettings();

            Serial.printf("White balance calibration successful! White reference: X=%d, Y=%d, Z=%d\n",
                         whiteX, whiteY, whiteZ);
            server.send(200, "text/plain", "White balance calibration completed successfully");
        } else {
            Serial.println("Failed to perform white balance calibration");
            server.send(500, "text/plain", "White balance calibration failed - sensor error");
        }
    } else {
        Serial.printf("Insufficient valid white readings (%d/10). Object may be too dark or not detected.\n", validReadings);
        server.send(500, "text/plain", "White balance calibration failed - object too dark or not detected");
    }

    // Turn LED back to scanning state
    if (!isScanning) {
        setIlluminationLED(false);
    }

    Serial.println("=== WHITE BALANCE COMPLETED ===");
}

void handleDarkCalibration() {
    Serial.println("=== DARK CALIBRATION ===");

    // Turn off LED for dark calibration
    setIlluminationLED(false);

    // Perform dark calibration
    bool success = colorSensor.performDarkCalibration(10);

    if (success) {
        currentSettings.hasDarkCalibration = true;
        currentSettings.isCalibrated = currentSettings.hasWhiteBalance && currentSettings.hasDarkCalibration;
        saveSettings();
        server.send(200, "text/plain", "Dark calibration completed successfully");
    } else {
        server.send(500, "text/plain", "Dark calibration failed");
    }

    Serial.println("=== DARK CALIBRATION COMPLETED ===");
}

void handleBlackCalibration() {
    Serial.println("=== BLACK CALIBRATION ===");
    Serial.println("Place a black reference object (black paper/fabric) under the sensor");

    // Turn on LED for black calibration (we need some light to measure black)
    setIlluminationLED(true);
    delay(500); // Allow LED to stabilize

    // Take multiple readings for black reference
    uint16_t blackX = 0, blackY = 0, blackZ = 0;
    int validReadings = 0;

    for (int i = 0; i < 10; i++) {
        delay(100);
        uint16_t x = colorSensor.getXData();
        uint16_t y = colorSensor.getYData();
        uint16_t z = colorSensor.getZData();

        // Only use readings that are reasonably low (black should be dark)
        if (x < 1000 && y < 1000 && z < 1000) {
            blackX += x;
            blackY += y;
            blackZ += z;
            validReadings++;
        }

        Serial.printf("Black sample %d: X=%d, Y=%d, Z=%d\n", i+1, x, y, z);
    }

    if (validReadings >= 5) {
        // Calculate average black reference
        blackX /= validReadings;
        blackY /= validReadings;
        blackZ /= validReadings;

        // Use the existing dark calibration method which is similar to black calibration
        bool success = colorSensor.performDarkCalibration(validReadings);

        if (success) {
            currentSettings.hasDarkCalibration = true;
            currentSettings.isCalibrated = currentSettings.hasWhiteBalance && currentSettings.hasDarkCalibration;
            saveSettings();

            Serial.printf("Black calibration successful! Black reference: X=%d, Y=%d, Z=%d\n",
                         blackX, blackY, blackZ);
            server.send(200, "text/plain", "Black calibration completed successfully");
        } else {
            Serial.println("Failed to perform black calibration");
            server.send(500, "text/plain", "Black calibration failed - sensor error");
        }
    } else {
        Serial.printf("Insufficient valid black readings (%d/10). Object may be too bright.\n", validReadings);
        server.send(500, "text/plain", "Black calibration failed - object too bright or not detected");
    }

    // Turn LED back to scanning state
    if (!isScanning) {
        setIlluminationLED(false);
    }

    Serial.println("=== BLACK CALIBRATION COMPLETED ===");
}

void handleOptimizeGain() {
    Serial.println("=== OPTIMIZING GAIN ===");

    uint8_t optimalGain = colorSensor.getOptimalGain();
    colorSensor.setALSGain(optimalGain);

    Serial.printf("Optimal gain set to: %d\n", optimalGain);

    JsonDocument doc;
    doc["optimal_gain"] = optimalGain;
    doc["gain_names"] = JsonArray();
    doc["gain_names"].add("1x");
    doc["gain_names"].add("4x");
    doc["gain_names"].add("16x");
    doc["gain_names"].add("64x");

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleGetSettings() {
    JsonDocument doc;
    doc["integration_time"] = currentSettings.integrationTime;
    doc["integration_time_ms"] = (currentSettings.integrationTime + 1) * 2.78f;
    doc["als_gain"] = currentSettings.alsGain;
    doc["als_gain_value"] = (1 << (currentSettings.alsGain * 2));
    doc["ir_compensation"] = currentSettings.irCompensation;
    doc["srgb_normalization"] = currentSettings.srgbNormalization;
    doc["adaptive_scaling"] = currentSettings.adaptiveScaling;
    doc["is_calibrated"] = currentSettings.isCalibrated;
    doc["has_white_balance"] = currentSettings.hasWhiteBalance;
    doc["has_dark_calibration"] = currentSettings.hasDarkCalibration;

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleSetSettings() {
    if (server.hasArg("plain")) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, server.arg("plain"));

        if (error) {
            server.send(400, "text/plain", "Invalid JSON");
            return;
        }

        // Update settings from JSON (using modern ArduinoJson syntax)
        if (!doc["integration_time"].isNull()) {
            currentSettings.integrationTime = doc["integration_time"];
        }
        if (!doc["als_gain"].isNull()) {
            currentSettings.alsGain = doc["als_gain"];
        }
        if (!doc["ir_compensation"].isNull()) {
            currentSettings.irCompensation = doc["ir_compensation"];
        }
        if (!doc["srgb_normalization"].isNull()) {
            currentSettings.srgbNormalization = doc["srgb_normalization"];
        }
        if (!doc["adaptive_scaling"].isNull()) {
            currentSettings.adaptiveScaling = doc["adaptive_scaling"];
        }

        // Apply settings to sensor and save
        applySensorSettings();
        saveSettings();

        server.send(200, "text/plain", "Settings updated successfully");
    } else {
        server.send(400, "text/plain", "No data provided");
    }
}

void handleResetSettings() {
    resetToDefaults();
    server.send(200, "text/plain", "Settings reset to defaults");
}

void handleLivePreview() {
    // Ensure LED is on for live preview in settings
    bool wasLEDOn = illuminationState;
    if (!wasLEDOn) {
        setIlluminationLED(true);
        delay(100); // Allow LED to stabilize
    }

    // Take a fresh reading for live preview
    readPrecisionColorSensor();

    // Return current color reading for live preview in settings
    JsonDocument doc;
    doc["rgb_r"] = currentColor.r;
    doc["rgb_g"] = currentColor.g;
    doc["rgb_b"] = currentColor.b;
    doc["hex_color"] = currentColor.hexColor;
    doc["confidence"] = currentColor.confidence;
    doc["is_saturated"] = currentColor.isSaturated;
    doc["raw_x"] = currentColor.x;
    doc["raw_y"] = currentColor.y;
    doc["raw_z"] = currentColor.z;

    // Include current settings for reference
    doc["integration_ms"] = (currentSettings.integrationTime + 1) * 2.78f;
    doc["gain_text"] = String((1 << (currentSettings.alsGain * 2))) + "x";
    doc["ir_comp"] = currentSettings.irCompensation;
    doc["normalization"] = currentSettings.srgbNormalization;
    doc["adaptive"] = currentSettings.adaptiveScaling;

    // Restore LED state if it was off
    if (!wasLEDOn && !isScanning) {
        setIlluminationLED(false);
    }

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

// ------------------------------------------------------------------------------------
// Sample Management API Handlers
// ------------------------------------------------------------------------------------
void handleGetSamples() {
    JsonDocument doc;
    doc["total_samples"] = totalSamples;
    doc["max_samples"] = MAX_STORED_SAMPLES;

    JsonArray samples = doc["samples"].to<JsonArray>();

    // Return samples in chronological order (newest first)
    for (int i = 0; i < totalSamples; i++) {
        int index = (currentSampleIndex - 1 - i + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;
        if (storedSamples[index].isValid) {
            JsonObject sample = samples.add<JsonObject>();
            sample["index"] = i;
            sample["r"] = storedSamples[index].r;
            sample["g"] = storedSamples[index].g;
            sample["b"] = storedSamples[index].b;
            sample["hex"] = storedSamples[index].hexColor;
            sample["dulux_name"] = storedSamples[index].duluxName;
            sample["dulux_r"] = storedSamples[index].duluxR;
            sample["dulux_g"] = storedSamples[index].duluxG;
            sample["dulux_b"] = storedSamples[index].duluxB;
            sample["delta_e"] = storedSamples[index].deltaE;
            sample["confidence"] = storedSamples[index].confidence;
            sample["timestamp"] = storedSamples[index].timestamp;
        }
    }

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleClearSamples() {
    clearAllSamples();
    server.send(200, "text/plain", "All samples cleared");
}

void handleDeleteSample() {
    if (server.hasArg("index")) {
        int sampleIndex = server.arg("index").toInt();
        if (sampleIndex >= 0 && sampleIndex < totalSamples) {
            // Mark sample as invalid (simple deletion)
            int actualIndex = (currentSampleIndex - 1 - sampleIndex + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;
            storedSamples[actualIndex].isValid = false;

            // Compact the array by shifting samples
            for (int i = sampleIndex; i < totalSamples - 1; i++) {
                int fromIndex = (currentSampleIndex - 1 - i - 1 + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;
                int toIndex = (currentSampleIndex - 1 - i + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;
                storedSamples[toIndex] = storedSamples[fromIndex];
            }

            totalSamples--;
            if (totalSamples > 0) {
                currentSampleIndex = (currentSampleIndex - 1 + MAX_STORED_SAMPLES) % MAX_STORED_SAMPLES;
            } else {
                currentSampleIndex = 0;
            }

            saveSamplesToEEPROM();
            server.send(200, "text/plain", "Sample deleted");
        } else {
            server.send(400, "text/plain", "Invalid sample index");
        }
    } else {
        server.send(400, "text/plain", "No sample index provided");
    }
}

void setupWebServer() {
    // Initialize LittleFS
    if (!LittleFS.begin(true)) {
        Serial.println("An Error has occurred while mounting LittleFS");
        return;
    }
    Serial.println("LittleFS mounted successfully");

    // List files in LittleFS for debugging
    Serial.println("Files in LittleFS:");
    File root = LittleFS.open("/");
    File file = root.openNextFile();
    while (file) {
        Serial.print("  ");
        Serial.println(file.name());
        file = root.openNextFile();
    }
    root.close();

    // Load Dulux color database
    if (!loadDuluxDatabase()) {
        // Use fallback colors if dulux.json fails to load
        Serial.println("Using fallback color database");
        COLOR_DATABASE_SIZE = FALLBACK_COLOR_COUNT;
        for (int i = 0; i < FALLBACK_COLOR_COUNT; i++) {
            strncpy(colorDatabase[i].name, fallbackColors[i].name, MAX_COLOR_NAME_LEN - 1);
            colorDatabase[i].name[MAX_COLOR_NAME_LEN - 1] = '\0';
            colorDatabase[i].r = fallbackColors[i].r;
            colorDatabase[i].g = fallbackColors[i].g;
            colorDatabase[i].b = fallbackColors[i].b;
        }
    }

    // Setup web server routes
    server.on("/", handleRoot);
    server.on("/style.css", handleStyleCSS);

    // Main data endpoint (enhanced with precision measurement data)
    server.on("/fulldata", handleFullData);

    // Settings API for live preview and configuration
    server.on("/live_preview", handleLivePreview);
    server.on("/get_settings", HTTP_GET, handleGetSettings);
    server.on("/set_settings", HTTP_POST, handleSetSettings);
    server.on("/reset_settings", HTTP_POST, handleResetSettings);

    // Scanning and LED control
    server.on("/start_scan", HTTP_POST, handleStartScan);
    server.on("/stop_scan", HTTP_POST, handleStopScan);
    server.on("/toggle_led", HTTP_POST, handleToggleLED);

    // Sample management
    server.on("/get_samples", HTTP_GET, handleGetSamples);
    server.on("/clear_samples", HTTP_POST, handleClearSamples);
    server.on("/delete_sample", HTTP_POST, handleDeleteSample);

    // Calibration functions
    server.on("/white_balance", HTTP_POST, handleWhiteBalance);
    server.on("/dark_calibration", HTTP_POST, handleDarkCalibration);
    server.on("/black_calibration", HTTP_POST, handleBlackCalibration);
    server.on("/optimize_gain", HTTP_POST, handleOptimizeGain);

    // Start server
    server.begin();
    Serial.println("Web server started");
    Serial.print("Open http://");
    Serial.print(WiFi.localIP());
    Serial.println(" in your browser");
}

// ------------------------------------------------------------------------------------
// WiFi Functions
// ------------------------------------------------------------------------------------
void initializeWiFi() {
    Serial.println("============================================================");
    Serial.println("WiFi Initialization");
    Serial.println("============================================================");
    Serial.printf("SSID: %s\n", WIFI_SSID);
    Serial.printf("Static IP: %s\n", STATIC_IP.toString().c_str());
    Serial.printf("Gateway: %s\n", GATEWAY.toString().c_str());
    Serial.printf("Subnet: %s\n", SUBNET.toString().c_str());

    // Disconnect any previous connection
    WiFi.disconnect(true);
    delay(1000);

    WiFi.mode(WIFI_STA);

    // Try without static IP first
    Serial.println("Attempting connection with DHCP...");
    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);

    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 30) {
        delay(500);
        Serial.print(".");
        attempts++;

        // Print WiFi status for debugging
        if (attempts % 10 == 0) {
            Serial.println();
            Serial.printf("WiFi Status: %d (attempt %d/30)\n", WiFi.status(), attempts);
        }
    }

    if (WiFi.status() == WL_CONNECTED) {
        wifiConnected = true;
        Serial.println();
        Serial.println("WiFi connected with DHCP!");
        Serial.printf("IP address: %s\n", WiFi.localIP().toString().c_str());
        Serial.printf("Gateway: %s\n", WiFi.gatewayIP().toString().c_str());
        Serial.printf("Subnet: %s\n", WiFi.subnetMask().toString().c_str());
        Serial.printf("DNS: %s\n", WiFi.dnsIP().toString().c_str());

        // Now try to set static IP
        Serial.println("Setting static IP...");
        if (WiFi.config(STATIC_IP, GATEWAY, SUBNET)) {
            Serial.printf("Static IP set successfully: %s\n", WiFi.localIP().toString().c_str());
        } else {
            Serial.println("Failed to set static IP, using DHCP IP");
        }
    } else {
        Serial.println();
        Serial.println("WiFi connection failed - starting Access Point mode");
        Serial.printf("Final WiFi Status: %d\n", WiFi.status());

        // Start Access Point mode as fallback
        WiFi.mode(WIFI_AP);
        WiFi.softAP("ESP32-ColorSensor", "colormatching");
        IPAddress apIP = WiFi.softAPIP();
        Serial.printf("Access Point started: ESP32-ColorSensor\n");
        Serial.printf("Password: colormatching\n");
        Serial.printf("AP IP address: %s\n", apIP.toString().c_str());
        wifiConnected = false;
    }
    Serial.println("============================================================");
}

// ------------------------------------------------------------------------------------
// Main Functions
// ------------------------------------------------------------------------------------
void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("============================================================");
    Serial.println("ESP32-S3 Color Matching Device - Working Version");
    Serial.println("============================================================");
    
    // Initialize ProS3 onboard RGB LED
    Serial.printf("Initializing ProS3 RGB LED on GPIO%d...\n", RGB_LED_PIN);

    // Initialize NeoPixel with correct format for ProS3
    rgbLED.begin();
    rgbLED.setBrightness(77); // Set to 30% brightness (0.3 like MicroPython example: 255 * 0.3 = 77)
    rgbLED.clear();
    rgbLED.show();
    delay(100);

    // Test RGB LED with color sequence to verify it's working
    Serial.println("Testing RGB LED colors...");
    rgbLED.setPixelColor(0, rgbLED.Color(255, 0, 0)); // Red
    rgbLED.show();
    delay(500);
    rgbLED.setPixelColor(0, rgbLED.Color(0, 255, 0)); // Green
    rgbLED.show();
    delay(500);
    rgbLED.setPixelColor(0, rgbLED.Color(0, 0, 255)); // Blue
    rgbLED.show();
    delay(500);
    rgbLED.setPixelColor(0, rgbLED.Color(0, 0, 0)); // Off
    rgbLED.show();
    Serial.println("ProS3 RGB LED initialized with power control and tested successfully");

    // Initialize GPIO pins
    pinMode(ILLUMINATION_LED_PIN, OUTPUT);
    pinMode(SENSOR_INTERRUPT_PIN, INPUT_PULLUP);

    digitalWrite(ILLUMINATION_LED_PIN, LOW);
    
    // Initialize I2C
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
    Wire.setClock(400000); // 400kHz
    Serial.println("I2C initialized");
    
    // Initialize color sensor
    sensorInitialized = initializeSensor();
    if (!sensorInitialized) {
        Serial.println("CRITICAL: Sensor initialization failed!");
        Serial.println("Check I2C connections and sensor power");
    }

    // Load saved samples from EEPROM
    loadSamplesFromEEPROM();
    
    // Initialize WiFi
    initializeWiFi();

    // Initialize Web Server (always start, even without WiFi for AP mode or later connection)
    setupWebServer();

    // Initialize illumination LED to OFF state
    setIlluminationLED(false);
    illuminationState = false;

    Serial.println("============================================================");
    Serial.println("System initialization complete!");
    Serial.println("Illumination LED: OFF (will turn on during scanning/calibration)");
    Serial.println("Color readings: PAUSED (press scan button to start)");
    Serial.println("============================================================");
}

void loop() {
    // Update RGB LED to show system is alive with color cycling
    updateRGBStatusLED();

    // LED control - respect manual toggle state or automatic scanning control
    if (isScanning) {
        setIlluminationLED(true);  // Always on during scanning
    } else {
        setIlluminationLED(manualLEDToggle);  // Use manual toggle state when not scanning
    }

    // Read color sensor at specified interval - always read but only print when scanning
    if (sensorInitialized && millis() - lastReading >= READING_INTERVAL_MS) {
        lastReading = millis();

        if (readPrecisionColorSensor()) {
            // Only print detailed color data when actively scanning
            if (isScanning) {
                Serial.println("=== PRECISION COLOR MEASUREMENT ===");
                Serial.printf("Raw XYZ: X=%d, Y=%d, Z=%d\n", currentColor.x, currentColor.y, currentColor.z);
                if (currentColor.isCalibrated) {
                    Serial.printf("Calibrated XYZ: X=%.3f, Y=%.3f, Z=%.3f\n",
                                 currentColor.calibratedX, currentColor.calibratedY, currentColor.calibratedZ);
                }
                Serial.printf("RGB: R=%d, G=%d, B=%d\n", currentColor.r, currentColor.g, currentColor.b);
                Serial.printf("Hex: %s\n", currentColor.hexColor);
                Serial.printf("IR: IR1=%d, IR2=%d\n", currentColor.ir1, currentColor.ir2);
                Serial.printf("Confidence: %.1f%%\n", currentColor.confidence);
                Serial.printf("Saturated: %s\n", currentColor.isSaturated ? "YES" : "NO");
                Serial.printf("Settings: Gain=%dx, Integration=%.1fms\n",
                             (1 << (currentSettings.alsGain * 2)),
                             (currentSettings.integrationTime + 1) * 2.78f);
                Serial.println("====================================");
            }
        }
    }

    // Handle web server requests (always handle, even without WiFi)
    server.handleClient();

    // Memory monitoring (every 30 seconds)
    static unsigned long lastMemoryCheck = 0;
    if (millis() - lastMemoryCheck > 30000) {
        Serial.printf("Memory Status - Free Heap: %d bytes, PSRAM: %d bytes\n",
                     ESP.getFreeHeap(), ESP.getFreePsram());
        lastMemoryCheck = millis();
    }

    // Small delay to prevent overwhelming the system
    delay(10);
}
