// ====================================================================================
// ESP32PROS3 Color Matcher - Full Web UI Edition with Enhanced Network
//
// Hosts a comprehensive web UI for real-time color matching, live data display,
// and on-the-fly sensor calibration. Uses advanced network setup with WiFi, OTA,
// WebSocket logging, and online color database fetching.
//
// REQUIRES:
// 1. ESP32 Board Support Package installed in Arduino IDE.
// 2. DFRobot_TCS3430 library installed via Arduino Library Manager.
// 3. ESPAsyncWebServer, AsyncTCP, Arduino_JSON, LittleFS, ArduinoOTA, and HTTPClient libraries installed.
//
// TCS3430 Wiring:
// - SDA: IO8
// - SCL: IO9
// - INT: IO21
// - 4.7kΩ pull-ups to 3.3V on SDA and SCL
// LEDs:
// - Indicator LED: IO5 (with 220Ω resistor)
// - Illumination LED: IO4 (with appropriate resistor)
// ====================================================================================

// ------------------------------------------------------------------------------------
// Section 1: Configuration
// ------------------------------------------------------------------------------------
#define I2C_SDA_PIN          8
#define I2C_SCL_PIN          9
#define INDICATOR_LED_PIN    5
#define ILLUMINATION_LED_PIN 4 // Second LED for illuminating object
#define READING_INTERVAL_MS  500 // 500ms = 2Hz refresh rate
#define SENSOR_INTERRUPT_PIN 21
#define LOG_LEVEL_ERROR      0
#define LOG_LEVEL_WARN       1
#define LOG_LEVEL_INFO       2
#define LOG_LEVEL_DEBUG      3
#define CURRENT_LOG_LEVEL    LOG_LEVEL_INFO // Reduced for stability
#define MAX_COLOR_NAME_LEN   25
#define FORMAT_LITTLEFS_IF_FAILED true
#define CONFIG_ASYNC_TCP_MAX_ACK_TIME  5000 // Reduced for faster recovery
#define CONFIG_ASYNC_TCP_PRIORITY      2
#define CONFIG_ASYNC_TCP_QUEUE_SIZE    64 // Reduced to save memory
#define CONFIG_ASYNC_TCP_RUNNING_CORE  0
#define CONFIG_ASYNC_TCP_STACK_SIZE    8192 // Reduced to avoid memory issues
#define NETWORK_TASK_STACK_SIZE        8192 // Reduced to avoid memory issues
#define WIFI_CHECK_INTERVAL    10000 // Increased to reduce frequent checks
#define OTA_REFRESH_INTERVAL   300000
#define WIFI_MAX_RETRIES       2 // Reduced to avoid long hangs
#define WIFI_RETRY_DELAY_MS    3000 // Reduced for faster retries
#define DISABLE_TWDT           true // Disable TWDT to prevent resets
#define DELTA_E_THRESHOLD      5.0  // Threshold for confident scan result

// ------------------------------------------------------------------------------------
// Section 1.5: Network Configuration
// ------------------------------------------------------------------------------------
const char* SSID = "Wifi 6"; // Verify this matches your network SSID (case-sensitive)
const char* PASSWORD = "Scrofani1985"; // Verify this matches your network password
const IPAddress STATIC_IP(192, 168, 0, 201);
const IPAddress GATEWAY(192, 168, 0, 1);
const IPAddress SUBNET(255, 255, 255, 0);
const IPAddress DNS_SERVER_PRIMARY(8, 8, 8, 8);
const IPAddress DNS_SERVER_SECONDARY(8, 8, 4, 4);

// ------------------------------------------------------------------------------------
// Section 2: Library Includes
// ------------------------------------------------------------------------------------
#include <Wire.h>
#include <DFRobot_TCS3430.h>
#include <Preferences.h>
#include <math.h>
#include <nvs_flash.h>
#include <WiFi.h>
#include <ESPAsyncWebServer.h>
#include <AsyncTCP.h>
#include <Arduino_JSON.h>
#include <LittleFS.h>
#include <ArduinoOTA.h>
#include <esp_task_wdt.h>
#include <esp_system.h>
#include <HTTPClient.h> // Added for online color database

// ------------------------------------------------------------------------------------
// Section 2.5: Global Type Definitions
// ------------------------------------------------------------------------------------
typedef struct { uint8_t r; uint8_t g; uint8_t b; } rgb_color_s;
typedef struct { float l; float a; float b; } lab_color_s;
typedef struct { float x; float y; float z; } xyz_color_s;

typedef struct {
    uint8_t measured_r, measured_g, measured_b;
    char matched_name[MAX_COLOR_NAME_LEN];
    uint8_t matched_r, matched_g, matched_b;
    float delta_e;
    char confidence[10];
    float avg_x, avg_y, avg_z;
    float avg_l, avg_a, avg_b;
    float avg_ir1, avg_ir2;
    bool data_ready;
} web_ui_data_t;

typedef struct {
    float k_ir_compensation_factor;
    float srgb_output_norm_factor;
    uint8_t als_gain;
    uint8_t integration_time;
    bool adaptive_scaling;
    bool data_is_valid;
} app_calibration_data_t;

// ------------------------------------------------------------------------------------
// Section 2.6: Global Variables
// ------------------------------------------------------------------------------------
volatile bool g_led_state = false; // Tracks indicator LED state
volatile bool g_illum_led_state = false; // Tracks illumination LED state
volatile bool g_is_scanning = false; // Tracks scan state
DFRobot_TCS3430 tcs3430_sensor;
volatile bool color_change_interrupt_flag = false;
uint32_t last_sensor_read_time = 0;
char logBuffer[256];
web_ui_data_t g_web_data;
app_calibration_data_t current_app_calibration;
Preferences preferences_calib;
const char* CALIB_NVS_NAMESPACE = "clr_calib";
const char* CALIB_NVS_KEY = "cal_data";
AsyncWebServer server(80);
AsyncWebSocket ws("/ws");
SemaphoreHandle_t printSemaphore = xSemaphoreCreateMutex();
volatile int debugLevel = 2;
TaskHandle_t networkTaskHandle = nullptr;
volatile bool wifiConnected = false;

// ------------------------------------------------------------------------------------
// Section 3: Web Server Handlers
// ------------------------------------------------------------------------------------
void handleStyleCss(AsyncWebServerRequest *request) {
    if (!LittleFS.exists("/style.css")) {
        request->send(500, "text/plain", "style.css not found");
        return;
    }
    request->send(LittleFS, "/style.css", "text/css");
}

void handleSetAdvancedSettings(AsyncWebServerRequest *request) {
    bool success = true;
    if (request->hasParam("integration_time", true)) {
        String val = request->getParam("integration_time", true)->value();
        uint8_t integration_time = (uint8_t) strtol(val.c_str(), nullptr, 16);
        if (integration_time == 0x01 || integration_time == 0x11 || integration_time == 0x23 || integration_time == 0x40 || integration_time == 0xFF) {
            current_app_calibration.integration_time = integration_time;
            tcs3430_sensor.setIntegrationTime(integration_time);
        } else {
            success = false;
        }
    } else {
        success = false;
    }
    if (request->hasParam("adaptive_scaling", true)) {
        String val = request->getParam("adaptive_scaling", true)->value();
        if (val == "true") {
            current_app_calibration.adaptive_scaling = true;
        } else if (val == "false") {
            current_app_calibration.adaptive_scaling = false;
        } else {
            success = false;
        }
    } else {
        success = false;
    }
    if (success) {
        calibration_save();
        request->send(200, "text/plain", "Advanced settings saved");
    } else {
        request->send(400, "text/plain", "Invalid parameters");
    }
}

void handleGetPreviewData(AsyncWebServerRequest *request) {
    JSONVar jsonObj;
    jsonObj["current_rgb"] = JSONVar();
    jsonObj["current_rgb"][0] = g_web_data.measured_r;
    jsonObj["current_rgb"][1] = g_web_data.measured_g;
    jsonObj["current_rgb"][2] = g_web_data.measured_b;
    jsonObj["new_rgb"] = JSONVar();
    jsonObj["new_rgb"][0] = g_web_data.measured_r;
    jsonObj["new_rgb"][1] = g_web_data.measured_g;
    jsonObj["new_rgb"][2] = g_web_data.measured_b;
    jsonObj["current_xyz"] = JSONVar();
    jsonObj["current_xyz"][0] = g_web_data.avg_x;
    jsonObj["current_xyz"][1] = g_web_data.avg_y;
    jsonObj["current_xyz"][2] = g_web_data.avg_z;
    jsonObj["new_xyz"] = JSONVar();
    jsonObj["new_xyz"][0] = g_web_data.avg_x;
    jsonObj["new_xyz"][1] = g_web_data.avg_y;
    jsonObj["new_xyz"][2] = g_web_data.avg_z;
    request->send(200, "application/json", JSON.stringify(jsonObj));
}

void handleGetCurrentSettings(AsyncWebServerRequest *request) {
    JSONVar jsonObj;
    jsonObj["integration_time"] = String("0x") + String(current_app_calibration.integration_time, HEX);
    jsonObj["adaptive_scaling"] = current_app_calibration.adaptive_scaling ? "true" : "false";
    request->send(200, "application/json", JSON.stringify(jsonObj));
}

void handleRefreshColorDB(AsyncWebServerRequest *request) {
    if (!wifiConnected) {
        request->send(503, "text/plain", "WiFi not connected");
        return;
    }
    if (fetchColorDatabase()) {
        char msg[50];
        snprintf(msg, sizeof(msg), "Color database refreshed: %d colors loaded", COLOR_DB_SIZE);
        request->send(200, "text/plain", msg);
    } else {
        request->send(500, "text/plain", "Failed to refresh color database");
    }
}

void setupServerRoutes() {
    server.on("/", HTTP_GET, handleRoot);
    server.on("/style.css", HTTP_GET, handleStyleCss);
    server.on("/fulldata", HTTP_GET, handleFullData);
    server.on("/set_calibration", HTTP_POST, handleSetCalibration);
    server.on("/toggle_led", HTTP_POST, handleToggleLed);
    server.on("/start_scan", HTTP_POST, handleStartScan);
    server.on("/stop_scan", HTTP_POST, handleStopScan);
    server.on("/white_balance", HTTP_POST, handleWhiteBalanceCalibration);
    server.on("/set_advanced_settings", HTTP_POST, handleSetAdvancedSettings);
    server.on("/get_preview_data", HTTP_GET, handleGetPreviewData);
    server.on("/get_current_settings", HTTP_GET, handleGetCurrentSettings);
    server.on("/refresh_colors", HTTP_POST, handleRefreshColorDB); // Added
    ws.onEvent(onWebSocketEvent);
    server.addHandler(&ws);
    server.begin();
}

void printToWebSerial(String message, int level = 2) {
    if (level > debugLevel && level != 0) return;
    if (xSemaphoreTake(printSemaphore, pdMS_TO_TICKS(100)) == pdTRUE) {
        String output = "[" + String(millis()) + "] " + message;
        Serial.println(output);
        static unsigned long lastSent = 0;
        if (level != 0 && millis() - lastSent < 200) {
            xSemaphoreGive(printSemaphore);
            return;
        }
        lastSent = millis();
        static unsigned long lastWebSocketRetry = 0;
        static volatile bool webSocketReady = false;
        if (!webSocketReady && (millis() - lastWebSocketRetry > 5000)) {
            ws.enable(true);
            lastWebSocketRetry = millis();
        }
        if (webSocketReady && wifiConnected) {
            try {
                ws.textAll(output);
            } catch (...) {
                Serial.println("Error sending WebSocket message");
                webSocketReady = false;
            }
        }
        xSemaphoreGive(printSemaphore);
    }
}

void printWiFiDiagnostics() {
    printToWebSerial("WiFi Diagnostics - Status: " + String(WiFi.status()), 2);
    printToWebSerial("SSID: " + String(WiFi.SSID()), 2);
    printToWebSerial("RSSI: " + String(WiFi.RSSI()) + " dBm", 2);
    printToWebSerial("Channel: " + String(WiFi.channel()), 2);
}

void log_message(int level, const char* component, const char* message) {
    if (level > CURRENT_LOG_LEVEL) return;
    const char* level_str;
    switch (level) {
        case LOG_LEVEL_ERROR: level_str = "ERROR"; break;
        case LOG_LEVEL_WARN:  level_str = "WARN "; break;
        case LOG_LEVEL_INFO:  level_str = "INFO "; break;
        case LOG_LEVEL_DEBUG: level_str = "DEBUG"; break;
        default:              level_str = "UNKWN"; break;
    }
    String msg = String("[") + level_str + "] [" + component + "] " + message;
    printToWebSerial(msg, level);
}

// ------------------------------------------------------------------------------------
// Section 4: Color Conversion
// ------------------------------------------------------------------------------------
const xyz_color_s D65_WHITEPOINT = {95.047f, 100.0f, 108.883f};
const float MATRIX_XYZ_TO_SRGB[3][3] = {
    { 3.2404542f, -1.5371385f, -0.4985314f},
    {-0.9692660f,  1.8760108f,  0.0415560f},
    { 0.0556434f, -0.2040259f,  1.0572252f}
};

float srgb_gamma_correct(float linear_val) {
    if (linear_val <= 0.0f) return 0.0f;
    if (linear_val >= 1.0f) return 1.0f;
    if (linear_val <= 0.0031308f) return 12.92f * linear_val;
    return 1.055f * powf(linear_val, 1.0f / 2.4f) - 0.055f;
}

rgb_color_s xyz_to_srgb_for_output(const xyz_color_s &xyz_sensor_raw, float normalization_factor) {
    rgb_color_s rgb_out;
    if (normalization_factor <= 1e-6) normalization_factor = 65535.0f;
    float X_norm = xyz_sensor_raw.x / normalization_factor;
    float Y_norm = xyz_sensor_raw.y / normalization_factor;
    float Z_norm = xyz_sensor_raw.z / normalization_factor;
    float R_linear = MATRIX_XYZ_TO_SRGB[0][0] * X_norm + MATRIX_XYZ_TO_SRGB[0][1] * Y_norm + MATRIX_XYZ_TO_SRGB[0][2] * Z_norm;
    float G_linear = MATRIX_XYZ_TO_SRGB[1][0] * X_norm + MATRIX_XYZ_TO_SRGB[1][1] * Y_norm + MATRIX_XYZ_TO_SRGB[1][2] * Z_norm;
    float B_linear = MATRIX_XYZ_TO_SRGB[2][0] * X_norm + MATRIX_XYZ_TO_SRGB[2][1] * Y_norm + MATRIX_XYZ_TO_SRGB[2][2] * Z_norm;
    rgb_out.r = (uint8_t)constrain(srgb_gamma_correct(R_linear) * 255.0f, 0.0f, 255.0f);
    rgb_out.g = (uint8_t)constrain(srgb_gamma_correct(G_linear) * 255.0f, 0.0f, 255.0f);
    rgb_out.b = (uint8_t)constrain(srgb_gamma_correct(B_linear) * 255.0f, 0.0f, 255.0f);
    return rgb_out;
}

float f_lab(float t) {
    const float epsilon = 216.0f / 24389.0f;
    const float kappa = 24389.0f / 27.0f;
    if (t > epsilon) return powf(t, 1.0f / 3.0f);
    return (kappa * t + 16.0f) / 116.0f;
}

void xyz_to_lab(const xyz_color_s &xyz_in, lab_color_s &lab_out, const xyz_color_s &white_point_ref) {
    float xr = xyz_in.x / white_point_ref.x;
    float yr = xyz_in.y / white_point_ref.y;
    float zr = xyz_in.z / white_point_ref.z;
    float fx = f_lab(xr); float fy = f_lab(yr); float fz = f_lab(zr);
    lab_out.l = 116.0f * fy - 16.0f;
    lab_out.a = 500.0f * (fx - fy);
    lab_out.b = 200.0f * (fy - fz);
}

float delta_e_cie76(const lab_color_s &lab1, const lab_color_s &lab2) {
    float dL = lab1.l - lab2.l; float da = lab1.a - lab2.a; float db = lab1.b - lab2.b;
    return sqrtf(dL * dL + da * da + db * db);
}

void apply_ir_compensation(xyz_color_s &xyz_data, uint16_t ir1_raw, uint16_t ir2_raw, float k_ir_comp_factor) {
    float ir_effect = k_ir_comp_factor * ((float)ir1_raw + (float)ir2_raw) / 2.0f;
    xyz_data.x = fmax(0.0f, xyz_data.x - ir_effect);
    xyz_data.y = fmax(0.0f, xyz_data.y - ir_effect);
    xyz_data.z = fmax(0.0f, xyz_data.z - ir_effect);
}

// ------------------------------------------------------------------------------------
// Section 5: Color Database
// ------------------------------------------------------------------------------------
#define MAX_ONLINE_COLORS 200 // Maximum colors to store
#define COLOR_CACHE_FILE "/colors.json" // LittleFS cache file

typedef struct {
    char name[MAX_COLOR_NAME_LEN];
    rgb_color_s rgb_ref;
    lab_color_s lab_ref;
    bool is_valid;
} reference_color_t;

reference_color_t color_database[MAX_ONLINE_COLORS];
int COLOR_DB_SIZE = 0;

bool fetchColorDatabase() {
    if (!wifiConnected) {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "Cannot fetch colors - WiFi not connected");
        return false;
    }

    HTTPClient http;
    String url = "https://www.thecolorapi.com/scheme?hex=FFFFFF&mode=monochrome&count=50";
    http.begin(url);
    http.addHeader("Content-Type", "application/json");

    int httpCode = http.GET();
    if (httpCode != HTTP_CODE_OK) {
        char msg[50];
        snprintf(msg, sizeof(msg), "HTTP GET failed: %d", httpCode);
        log_message(LOG_LEVEL_ERROR, "ColorDB", msg);
        http.end();
        return false;
    }

    String payload = http.getString();
    http.end();

    JSONVar colorData = JSON.parse(payload);
    if (JSON.typeof(colorData) == "undefined") {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "JSON parsing failed");
        return false;
    }

    if (!colorData.hasOwnProperty("colors")) {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "No 'colors' array in API response");
        return false;
    }

    JSONVar colorArray = colorData["colors"];
    COLOR_DB_SIZE = 0;
    memset(color_database, 0, sizeof(color_database));

    for (int i = 0; i < colorArray.length() && i < MAX_ONLINE_COLORS; i++) {
        if (colorArray[i].hasOwnProperty("hex") && colorArray[i].hasOwnProperty("rgb") &&
            colorArray[i].hasOwnProperty("name")) {
            strncpy(color_database[i].name, (const char*)colorArray[i]["name"]["value"], MAX_COLOR_NAME_LEN - 1);
            color_database[i].name[MAX_COLOR_NAME_LEN - 1] = '\0';

            color_database[i].rgb_ref.r = (uint8_t)(int)colorArray[i]["rgb"]["r"];
            color_database[i].rgb_ref.g = (uint8_t)(int)colorArray[i]["rgb"]["g"];
            color_database[i].rgb_ref.b = (uint8_t)(int)colorArray[i]["rgb"]["b"];
            color_database[i].is_valid = true;
            COLOR_DB_SIZE++;
        }
    }

    if (COLOR_DB_SIZE > 0) {
        if (saveCachedColors()) {
            char msg[50];
            snprintf(msg, sizeof(msg), "Cached %d colors to LittleFS", COLOR_DB_SIZE);
            log_message(LOG_LEVEL_INFO, "ColorDB", msg);
        }
        initialize_color_database();
        char msg[50];
        snprintf(msg, sizeof(msg), "Loaded %d colors from API", COLOR_DB_SIZE);
        log_message(LOG_LEVEL_INFO, "ColorDB", msg);
        return true;
    } else {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "No valid colors found in API response");
        return false;
    }
}

bool loadCachedColors() {
    if (!LittleFS.exists(COLOR_CACHE_FILE)) {
        log_message(LOG_LEVEL_WARN, "ColorDB", "No cached colors found");
        return false;
    }

    File file = LittleFS.open(COLOR_CACHE_FILE, "r");
    if (!file) {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "Failed to open cache file");
        return false;
    }

    String payload = file.readString();
    file.close();

    JSONVar colorArray = JSON.parse(payload);
    if (JSON.typeof(colorArray) == "undefined") {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "Failed to parse cached colors");
        return false;
    }

    COLOR_DB_SIZE = 0;
    memset(color_database, 0, sizeof(color_database));

    for (int i = 0; i < colorArray.length() && i < MAX_ONLINE_COLORS; i++) {
        if (colorArray[i].hasOwnProperty("name") && colorArray[i].hasOwnProperty("r") &&
            colorArray[i].hasOwnProperty("g") && colorArray[i].hasOwnProperty("b")) {
            strncpy(color_database[i].name, (const char*)colorArray[i]["name"], MAX_COLOR_NAME_LEN - 1);
            color_database[i].name[MAX_COLOR_NAME_LEN - 1] = '\0';

            color_database[i].rgb_ref.r = (uint8_t)(int)colorArray[i]["r"];
            color_database[i].rgb_ref.g = (uint8_t)(int)colorArray[i]["g"];
            color_database[i].rgb_ref.b = (uint8_t)(int)colorArray[i]["b"];
            color_database[i].is_valid = true;
            COLOR_DB_SIZE++;
        }
    }

    if (COLOR_DB_SIZE > 0) {
        initialize_color_database();
        char msg[50];
        snprintf(msg, sizeof(msg), "Loaded %d colors from cache", COLOR_DB_SIZE);
        log_message(LOG_LEVEL_INFO, "ColorDB", msg);
        return true;
    } else {
        log_message(LOG_LEVEL_WARN, "ColorDB", "No valid colors in cache");
        return false;
    }
}

bool saveCachedColors() {
    File file = LittleFS.open(COLOR_CACHE_FILE, "w");
    if (!file) {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "Failed to open cache file for writing");
        return false;
    }

    JSONVar colorArray = JSONVar();
    for (int i = 0; i < COLOR_DB_SIZE; i++) {
        if (color_database[i].is_valid) {
            JSONVar colorObj;
            colorObj["name"] = color_database[i].name;
            colorObj["r"] = color_database[i].rgb_ref.r;
            colorObj["g"] = color_database[i].rgb_ref.g;
            colorObj["b"] = color_database[i].rgb_ref.b;
            colorArray[i] = colorObj;
        }
    }

    String jsonString = JSON.stringify(colorArray);
    if (file.print(jsonString)) {
        file.close();
        return true;
    } else {
        log_message(LOG_LEVEL_ERROR, "ColorDB", "Failed to write to cache file");
        file.close();
        return false;
    }
}

void initialize_color_database() {
    if (COLOR_DB_SIZE == 0) {
        if (loadCachedColors()) {
            log_message(LOG_LEVEL_INFO, "ColorDB", "Using cached colors");
            return;
        }

        const reference_color_t builtin_colors[] = {
            { "Jasmine White", {245, 240, 220} }, { "Almond White", {240, 235, 210} },
            { "Primrose White", {242, 238, 215} }, { "Fine Cream", {238, 230, 210} },
            { "Antique White USA", {235, 230, 235} }, { "Whisper White", {240, 235, 215} }
        };
        const int BUILTIN_COLORS = sizeof(builtin_colors) / sizeof(builtin_colors[0]);

        for (int i = 0; i < BUILTIN_COLORS && i < MAX_ONLINE_COLORS; i++) {
            memcpy(&color_database[i], &builtin_colors[i], sizeof(reference_color_t));
            color_database[i].is_valid = true;
            COLOR_DB_SIZE++;
        }
        char msg[50];
        snprintf(msg, sizeof(msg), "Using %d built-in colors", COLOR_DB_SIZE);
        log_message(LOG_LEVEL_INFO, "ColorDB", msg);
    }

    for (int i = 0; i < COLOR_DB_SIZE; i++) {
        if (color_database[i].is_valid) {
            xyz_to_lab(srgb_to_xyz(color_database[i].rgb_ref), color_database[i].lab_ref, D65_WHITEPOINT);
        }
    }
}

int find_closest_color_in_db(const lab_color_s &measured_lab, float &out_delta_e) {
    float min_delta_e = 999999.0f;
    int closest_idx = -1;

    for (int i = 0; i < COLOR_DB_SIZE; i++) {
        if (color_database[i].is_valid) {
            float current_delta_e = delta_e_cie76(measured_lab, color_database[i].lab_ref);
            if (current_delta_e < min_delta_e) {
                min_delta_e = current_delta_e;
                closest_idx = i;
            }
        }
    }

    out_delta_e = min_delta_e;
    return closest_idx;
}

const float MATRIX_SRGB_TO_XYZ[3][3] = {
    {0.4124564f, 0.3575761f, 0.1804375f}, {0.2126729f, 0.7151522f, 0.0721750f}, {0.0193339f, 0.1191920f, 0.9503041f}
};

float srgb_inverse_gamma_correct(float srgb_val) {
    if (srgb_val <= 0.0f) return 0.0f; if (srgb_val >= 1.0f) return 1.0f;
    if (srgb_val <= 0.04045f) return srgb_val / 12.92f;
    return powf((srgb_val + 0.055f) / 1.055f, 2.4f);
}

xyz_color_s srgb_to_xyz(const rgb_color_s &srgb) {
    float r_linear = srgb_inverse_gamma_correct(srgb.r / 255.0f);
    float g_linear = srgb_inverse_gamma_correct(srgb.g / 255.0f);
    float b_linear = srgb_inverse_gamma_correct(srgb.b / 255.0f);
    xyz_color_s xyz_out;
    xyz_out.x = (MATRIX_SRGB_TO_XYZ[0][0] * r_linear + MATRIX_SRGB_TO_XYZ[0][1] * g_linear + MATRIX_SRGB_TO_XYZ[0][2] * b_linear) * 100.0f;
    xyz_out.y = (MATRIX_SRGB_TO_XYZ[1][0] * r_linear + MATRIX_SRGB_TO_XYZ[1][1] * g_linear + MATRIX_SRGB_TO_XYZ[1][2] * b_linear) * 100.0f;
    xyz_out.z = (MATRIX_SRGB_TO_XYZ[2][0] * r_linear + MATRIX_SRGB_TO_XYZ[2][1] * g_linear + MATRIX_SRGB_TO_XYZ[2][2] * b_linear) * 100.0f;
    return xyz_out;
}

// ------------------------------------------------------------------------------------
// Section 6: Calibration
// ------------------------------------------------------------------------------------
bool perform_white_balance_calibration() {
    log_message(LOG_LEVEL_INFO, "Calibration", "Starting white balance calibration...");
    digitalWrite(ILLUMINATION_LED_PIN, HIGH);
    g_illum_led_state = true;
    log_message(LOG_LEVEL_INFO, "Calibration", "Illumination LED ON");
    uint8_t tcs_address = 0x39;
    Wire.beginTransmission(tcs_address);
    Wire.write(0x06); Wire.write(0xE0);
    if (Wire.endTransmission() != 0) {
        log_message(LOG_LEVEL_ERROR, "Calibration", "Failed to clear interrupts");
        digitalWrite(ILLUMINATION_LED_PIN, LOW);
        g_illum_led_state = false;
        return false;
    }
    Wire.beginTransmission(tcs_address);
    Wire.write(0x00); Wire.write(0x03);
    if (Wire.endTransmission() != 0) {
        log_message(LOG_LEVEL_ERROR, "Calibration", "Failed to enable sensor");
        digitalWrite(ILLUMINATION_LED_PIN, LOW);
        g_illum_led_state = false;
        return false;
    }
    vTaskDelay(pdMS_TO_TICKS(100));
    uint8_t status = tcs3430_sensor.getDeviceStatus();
    if (!(status & 0x03)) {
        char status_msg[100];
        snprintf(status_msg, sizeof(status_msg), "Sensor not enabled: 0x%02X (PON:%d, AEN:%d)", status, (status & 0x01), (status & 0x02) >> 1);
        log_message(LOG_LEVEL_ERROR, "Calibration", status_msg);
        digitalWrite(ILLUMINATION_LED_PIN, LOW);
        g_illum_led_state = false;
        return false;
    }
    const int num_samples = 10;
    float sum_x = 0, sum_y = 0, sum_z = 0;
    for (int i = 0; i < num_samples; i++) {
        vTaskDelay(pdMS_TO_TICKS(100));
        sum_x += tcs3430_sensor.getXData();
        sum_y += tcs3430_sensor.getYData();
        sum_z += tcs3430_sensor.getZData();
    }
    float avg_x = sum_x / num_samples;
    float avg_y = sum_y / num_samples;
    float avg_z = sum_z / num_samples;
    digitalWrite(ILLUMINATION_LED_PIN, LOW);
    g_illum_led_state = false;
    log_message(LOG_LEVEL_INFO, "Calibration", "Illumination LED OFF");
    if (avg_y > 50.0f) {
        current_app_calibration.srgb_output_norm_factor = avg_y * 0.8f;
        log_message(LOG_LEVEL_INFO, "Calibration", "White balance calibration completed successfully.");
        char msg[100];
        snprintf(msg, sizeof(msg), "New normalization factor: %.1f (X:%.1f, Y:%.1f, Z:%.1f)", current_app_calibration.srgb_output_norm_factor, avg_x, avg_y, avg_z);
        log_message(LOG_LEVEL_INFO, "Calibration", msg);
        return true;
    } else {
        char msg[100];
        snprintf(msg, sizeof(msg), "Insufficient light for calibration (X:%.1f, Y:%.1f, Z:%.1f)", avg_x, avg_y, avg_z);
        log_message(LOG_LEVEL_WARN, "Calibration", msg);
        return false;
    }
}

rgb_color_s xyz_to_srgb_enhanced(const xyz_color_s &xyz_sensor_raw, float normalization_factor) {
    rgb_color_s rgb_out;
    if (normalization_factor <= 1e-6) {
        normalization_factor = 15000.0f;
    }
    float adaptive_scale = 1.0f;
    if (current_app_calibration.adaptive_scaling) {
        float luminance = xyz_sensor_raw.y;
        if (luminance > 0) {
            adaptive_scale = fmin(2.0f, fmax(0.5f, 10000.0f / luminance));
        }
    }
    float effective_norm = normalization_factor * adaptive_scale;
    float X_norm = xyz_sensor_raw.x / effective_norm;
    float Y_norm = xyz_sensor_raw.y / effective_norm;
    float Z_norm = xyz_sensor_raw.z / effective_norm;
    float R_linear = MATRIX_XYZ_TO_SRGB[0][0] * X_norm + MATRIX_XYZ_TO_SRGB[0][1] * Y_norm + MATRIX_XYZ_TO_SRGB[0][2] * Z_norm;
    float G_linear = MATRIX_XYZ_TO_SRGB[1][0] * X_norm + MATRIX_XYZ_TO_SRGB[1][1] * Y_norm + MATRIX_XYZ_TO_SRGB[1][2] * Z_norm;
    float B_linear = MATRIX_XYZ_TO_SRGB[2][0] * X_norm + MATRIX_XYZ_TO_SRGB[2][1] * Y_norm + MATRIX_XYZ_TO_SRGB[2][2] * Z_norm;
    R_linear = fmax(0.0f, R_linear);
    G_linear = fmax(0.0f, G_linear);
    B_linear = fmax(0.0f, B_linear);
    rgb_out.r = (uint8_t)constrain(srgb_gamma_correct(R_linear) * 255.0f, 0.0f, 255.0f);
    rgb_out.g = (uint8_t)constrain(srgb_gamma_correct(G_linear) * 255.0f, 0.0f, 255.0f);
    rgb_out.b = (uint8_t)constrain(srgb_gamma_correct(B_linear) * 255.0f, 0.0f, 255.0f);
    return rgb_out;
}

bool calibration_load() {
    if (!preferences_calib.begin(CALIB_NVS_NAMESPACE, true)) {
        current_app_calibration = { 0.02f, 15000.0f, 16, 0x23, true, false };
        log_message(LOG_LEVEL_WARN, "Calibration", "Failed to open NVS namespace. Using defaults.");
        return false;
    }
    if (preferences_calib.isKey(CALIB_NVS_KEY)) {
        size_t bytes = preferences_calib.getBytes(CALIB_NVS_KEY, (void*)&current_app_calibration, sizeof(app_calibration_data_t));
        preferences_calib.end();
        if (bytes == sizeof(app_calibration_data_t)) {
            log_message(LOG_LEVEL_INFO, "Calibration", "Calibration data loaded from NVS.");
            return true;
        } else {
            log_message(LOG_LEVEL_WARN, "Calibration", "Invalid calibration data size in NVS. Using defaults.");
        }
    } else {
        log_message(LOG_LEVEL_WARN, "Calibration", "No calibration data in NVS. Using defaults.");
    }
    preferences_calib.end();
    current_app_calibration = { 0.02f, 15000.0f, 16, 0x23, true, false };
    return false;
}

bool calibration_save() {
    if (!preferences_calib.begin(CALIB_NVS_NAMESPACE, false)) {
        log_message(LOG_LEVEL_ERROR, "Calibration", "Failed to open NVS namespace for writing.");
        return false;
    }
    size_t bytes = preferences_calib.putBytes(CALIB_NVS_KEY, (const void*)&current_app_calibration, sizeof(app_calibration_data_t));
    preferences_calib.end();
    if (bytes == sizeof(app_calibration_data_t)) {
        log_message(LOG_LEVEL_INFO, "Calibration", "Calibration data saved to NVS.");
        return true;
    } else {
        log_message(LOG_LEVEL_ERROR, "Calibration", "Failed to save calibration data to NVS.");
        return false;
    }
}

void calibration_init() {
    if (!calibration_load()) {
        log_message(LOG_LEVEL_WARN, "Calibration", "Failed to load calib data. Using defaults.");
        current_app_calibration = { 0.02f, 15000.0f, 16, 0x23, true, false };
    } else {
        log_message(LOG_LEVEL_INFO, "Calibration", "Calibration data loaded successfully.");
    }
    log_message(LOG_LEVEL_INFO, "Calibration", "Configuring TCS3430 sensor...");
    tcs3430_sensor.setALSGain(current_app_calibration.als_gain);
    char gain_msg[50];
    snprintf(gain_msg, sizeof(gain_msg), "ALS Gain set to: %dx", current_app_calibration.als_gain);
    log_message(LOG_LEVEL_INFO, "Calibration", gain_msg);
    tcs3430_sensor.setIntegrationTime(current_app_calibration.integration_time);
    char time_msg[50];
    snprintf(time_msg, sizeof(time_msg), "Integration time set to: 0x%02X (~%.1fms)", current_app_calibration.integration_time, current_app_calibration.integration_time * 2.78);
    log_message(LOG_LEVEL_INFO, "Calibration", time_msg);
    tcs3430_sensor.setWaitTimer(true);
    tcs3430_sensor.setWaitTime(0x00);
    log_message(LOG_LEVEL_INFO, "Calibration", "Wait timer configured");
    vTaskDelay(pdMS_TO_TICKS(100));
}

// ------------------------------------------------------------------------------------
// Section 7: Network & Web Server Functions
// ------------------------------------------------------------------------------------
String getWiFiStatusString(wl_status_t status) {
    switch (status) {
        case WL_NO_SHIELD: return "No WiFi Shield";
        case WL_IDLE_STATUS: return "Idle";
        case WL_NO_SSID_AVAIL: return "No SSID Available";
        case WL_SCAN_COMPLETED: return "Scan Completed";
        case WL_CONNECTED: return "Connected";
        case WL_CONNECT_FAILED: return "Connection Failed";
        case WL_CONNECTION_LOST: return "Connection Lost";
        case WL_DISCONNECTED: return "Disconnected";
        default: return String("Unknown (") + String(status) + ")";
    }
}

void attemptWiFiReconnect() {
    if (WiFi.status() != WL_CONNECTED && wifiConnected) {
        log_message(LOG_LEVEL_INFO, "WiFi", "Disconnected, attempting reconnect...");
        WiFi.reconnect();
        unsigned long start = millis();
        while (WiFi.status() != WL_CONNECTED && millis() - start < 5000) {
            vTaskDelay(pdMS_TO_TICKS(250));
        }
        if (WiFi.status() == WL_CONNECTED) {
            wifiConnected = true;
            char msg[50];
            snprintf(msg, sizeof(msg), "Reconnected, IP: %s", WiFi.localIP().toString().c_str());
            log_message(LOG_LEVEL_INFO, "WiFi", msg);
            printWiFiDiagnostics();
        } else {
            wifiConnected = false;
            char msg[50];
            snprintf(msg, sizeof(msg), "Reconnect failed, Status: %s", getWiFiStatusString(WiFi.status()).c_str());
            log_message(LOG_LEVEL_ERROR, "WiFi", msg);
        }
    }
}

bool initWiFi() {
    log_message(LOG_LEVEL_INFO, "WiFi", "Initializing WiFi...");
    WiFi.mode(WIFI_STA);
    vTaskDelay(pdMS_TO_TICKS(500));
    WiFi.config(STATIC_IP, GATEWAY, SUBNET, DNS_SERVER_PRIMARY, DNS_SERVER_SECONDARY);
    WiFi.begin(SSID, PASSWORD);
    unsigned long start = millis();
    while (WiFi.status() != WL_CONNECTED && millis() - start < 8000) {
        vTaskDelay(pdMS_TO_TICKS(250));
    }
    if (WiFi.status() == WL_CONNECTED) {
        wifiConnected = true;
        char msg[50];
        snprintf(msg, sizeof(msg), "Connected with static IP, IP: %s", WiFi.localIP().toString().c_str());
        log_message(LOG_LEVEL_INFO, "WiFi", msg);
        printWiFiDiagnostics();
        return true;
    }
    log_message(LOG_LEVEL_WARN, "WiFi", "Static IP failed, trying dynamic IP...");
    WiFi.config(INADDR_NONE, INADDR_NONE, INADDR_NONE);
    WiFi.begin(SSID, PASSWORD);
    start = millis();
    while (WiFi.status() != WL_CONNECTED && millis() - start < 8000) {
        vTaskDelay(pdMS_TO_TICKS(250));
    }
    if (WiFi.status() == WL_CONNECTED) {
        wifiConnected = true;
        char msg[50];
        snprintf(msg, sizeof(msg), "Connected with dynamic IP, IP: %s", WiFi.localIP().toString().c_str());
        log_message(LOG_LEVEL_INFO, "WiFi", msg);
        printWiFiDiagnostics();
        return true;
    }
    wifiConnected = false;
    char msg[50];
    snprintf(msg, sizeof(msg), "Connection failed, Status: %s", getWiFiStatusString(WiFi.status()).c_str());
    log_message(LOG_LEVEL_ERROR, "WiFi", msg);
    return false;
}

void onWebSocketEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type, void *arg, uint8_t *data, size_t len) {
    static volatile bool webSocketReady = false;
    switch (type) {
        case WS_EVT_CONNECT: {
            webSocketReady = true;
            char msg[50];
            snprintf(msg, sizeof(msg), "Client connected, ID: %d", client->id());
            log_message(LOG_LEVEL_INFO, "WebSocket", msg);
            break;
        }
        case WS_EVT_DISCONNECT: {
            webSocketReady = false;
            char msg[50];
            snprintf(msg, sizeof(msg), "Client disconnected, ID: %d", client->id());
            log_message(LOG_LEVEL_INFO, "WebSocket", msg);
            break;
        }
        case WS_EVT_DATA: break;
        case WS_EVT_ERROR: {
            webSocketReady = false;
            log_message(LOG_LEVEL_ERROR, "WebSocket", "Error occurred");
            break;
        }
    }
}

void handleRoot(AsyncWebServerRequest *request) {
    if (!LittleFS.exists("/index.html")) {
        request->send(500, "text/plain", "index.html not found");
        return;
    }
    request->send(LittleFS, "/index.html", "text/html");
}

void handleFullData(AsyncWebServerRequest *request) {
    JSONVar jsonObj;
    jsonObj["data_ready"] = g_web_data.data_ready;
    jsonObj["measured_r"] = g_web_data.measured_r; jsonObj["measured_g"] = g_web_data.measured_g; jsonObj["measured_b"] = g_web_data.measured_b;
    jsonObj["matched_name"] = g_web_data.matched_name;
    jsonObj["matched_r"] = g_web_data.matched_r; jsonObj["matched_g"] = g_web_data.matched_g; jsonObj["matched_b"] = g_web_data.matched_b;
    jsonObj["delta_e"] = g_web_data.delta_e; jsonObj["confidence"] = g_web_data.confidence;
    jsonObj["avg_x"] = g_web_data.avg_x; jsonObj["avg_y"] = g_web_data.avg_y; jsonObj["avg_z"] = g_web_data.avg_z;
    jsonObj["avg_l"] = g_web_data.avg_l; jsonObj["avg_a"] = g_web_data.avg_a; jsonObj["avg_b"] = g_web_data.avg_b;
    jsonObj["avg_ir1"] = g_web_data.avg_ir1; jsonObj["avg_ir2"] = g_web_data.avg_ir2;
    jsonObj["calib_gain"] = current_app_calibration.als_gain;
    jsonObj["calib_ir_comp"] = current_app_calibration.k_ir_compensation_factor;
    jsonObj["calib_norm"] = current_app_calibration.srgb_output_norm_factor;
    jsonObj["led_state"] = g_led_state;
    jsonObj["is_scanning"] = g_is_scanning;
    request->send(200, "application/json", JSON.stringify(jsonObj));
}

void handleSetCalibration(AsyncWebServerRequest *request) {
    bool success = true;
    if (request->hasParam("gain", true)) {
        int gain = request->getParam("gain", true)->value().toInt();
        if (gain == 1 || gain == 4 || gain == 16 || gain == 64) {
            current_app_calibration.als_gain = gain;
            tcs3430_sensor.setALSGain(gain);
        } else success = false;
    }
    if (request->hasParam("ir_comp", true)) {
        float ir_comp = request->getParam("ir_comp", true)->value().toFloat();
        if (ir_comp >= 0.0 && ir_comp <= 1.0) { current_app_calibration.k_ir_compensation_factor = ir_comp; } else success = false;
    }
    if (request->hasParam("norm", true)) {
        float norm = request->getParam("norm", true)->value().toFloat();
        if (norm >= 1000.0 && norm <= 100000.0) { current_app_calibration.srgb_output_norm_factor = norm; } else success = false;
    }
    if (success) {
        current_app_calibration.data_is_valid = true;
        calibration_save();
        log_message(LOG_LEVEL_INFO, "WebCalib", "Calibration updated and saved.");
        request->send(200, "text/plain", "Saved!");
    } else {
        log_message(LOG_LEVEL_ERROR, "WebCalib", "Invalid calibration data.");
        request->send(400, "text/plain", "Invalid Value!");
    }
}

void handleToggleLed(AsyncWebServerRequest *request) {
    g_led_state = !g_led_state;
    digitalWrite(INDICATOR_LED_PIN, g_led_state ? HIGH : LOW);
    log_message(LOG_LEVEL_INFO, "LED", g_led_state ? "Indicator LED ON" : "Indicator LED OFF");
    JSONVar response;
    response["led_state"] = g_led_state;
    request->send(200, "application/json", JSON.stringify(response));
}

void handleStartScan(AsyncWebServerRequest *request) {
    if (!g_is_scanning) {
        g_is_scanning = true;
        g_led_state = true;
        g_illum_led_state = true;
        digitalWrite(INDICATOR_LED_PIN, HIGH);
        digitalWrite(ILLUMINATION_LED_PIN, HIGH);
        g_web_data.data_ready = false;
        strcpy(g_web_data.matched_name, "Scanning...");
        strcpy(g_web_data.confidence, "N/A");
        log_message(LOG_LEVEL_INFO, "Scan", "Scan started, LEDs ON");
        request->send(200, "text/plain", "Scan started");
    } else {
        request->send(400, "text/plain", "Scan already in progress");
    }
}

void handleStopScan(AsyncWebServerRequest *request) {
    if (g_is_scanning) {
        g_is_scanning = false;
        g_led_state = false;
        g_illum_led_state = false;
        digitalWrite(INDICATOR_LED_PIN, LOW);
        digitalWrite(ILLUMINATION_LED_PIN, LOW);
        g_web_data.data_ready = false;
        strcpy(g_web_data.matched_name, "Scan stopped");
        strcpy(g_web_data.confidence, "N/A");
        log_message(LOG_LEVEL_INFO, "Scan", "Scan stopped, LEDs OFF");
        request->send(200, "text/plain", "Scan stopped");
    } else {
        request->send(400, "text/plain", "No scan in progress");
    }
}

void handleWhiteBalanceCalibration(AsyncWebServerRequest *request) {
    if (!g_is_scanning) {
        g_led_state = true;
        digitalWrite(INDICATOR_LED_PIN, HIGH);
        if (perform_white_balance_calibration()) {
            calibration_save();
            log_message(LOG_LEVEL_INFO, "WebCalib", "White balance calibration completed.");
            request->send(200, "text/plain", "Calibration completed!");
        } else {
            log_message(LOG_LEVEL_ERROR, "WebCalib", "Calibration failed.");
            request->send(400, "text/plain", "Calibration failed: insufficient light or sensor error.");
        }
        g_led_state = false;
        digitalWrite(INDICATOR_LED_PIN, LOW);
    } else {
        request->send(400, "text/plain", "Cannot calibrate while scanning");
    }
}

void handleRefreshColorDB(AsyncWebServerRequest *request) {
    if (!wifiConnected) {
        request->send(503, "text/plain", "WiFi not connected");
        return;
    }
    if (fetchColorDatabase()) {
        char msg[50];
        snprintf(msg, sizeof(msg), "Color database refreshed: %d colors loaded", COLOR_DB_SIZE);
        request->send(200, "text/plain", msg);
    } else {
        request->send(500, "text/plain", "Failed to refresh color database");
    }
}

void TaskNetwork(void *pvParameters) {
    bool otaInProgress = false;
    if (initWiFi()) {
        setupServerRoutes();
        log_message(LOG_LEVEL_INFO, "Network", "HTTP server started");
        ArduinoOTA.setHostname("ColorMatcher");
        ArduinoOTA.onStart([&]() {
            otaInProgress = true;
            log_message(LOG_LEVEL_INFO, "OTA", "Update started");
        });
        ArduinoOTA.onEnd([&]() {
            otaInProgress = false;
            log_message(LOG_LEVEL_INFO, "OTA", "Update completed");
        });
        ArduinoOTA.onError([&](ota_error_t error) {
            otaInProgress = false;
            char msg[50];
            snprintf(msg, sizeof(msg), "Error: %d", error);
            log_message(LOG_LEVEL_ERROR, "OTA", msg);
        });
        ArduinoOTA.onProgress([](unsigned int progress, unsigned int total) {});
        ArduinoOTA.begin();
        log_message(LOG_LEVEL_INFO, "OTA", "Initialized");
    } else {
        log_message(LOG_LEVEL_ERROR, "Network", "Web server and OTA not started due to WiFi failure");
    }
    unsigned long lastWiFiCheck = 0;
    unsigned long lastOTARefresh = 0;
    for (;;) {
        unsigned long currentMillis = millis();
        if (currentMillis - lastWiFiCheck >= WIFI_CHECK_INTERVAL) {
            if (!wifiConnected) {
                log_message(LOG_LEVEL_INFO, "Network", "Attempting WiFi reconnect...");
                if (initWiFi()) {
                    setupServerRoutes();
                    log_message(LOG_LEVEL_INFO, "Network", "HTTP server started");
                    ArduinoOTA.begin();
                    log_message(LOG_LEVEL_INFO, "OTA", "Initialized");
                }
            } else {
                attemptWiFiReconnect();
            }
            lastWiFiCheck = currentMillis;
        }
        if (wifiConnected) {
            ArduinoOTA.handle();
            if (!otaInProgress && currentMillis - lastOTARefresh >= OTA_REFRESH_INTERVAL) {
                ws.cleanupClients();
                lastOTARefresh = currentMillis;
            }
        }
        vTaskDelay(pdMS_TO_TICKS(100));
    }
}

void TaskColorDBRefresh(void *pvParameters) {
    const uint32_t REFRESH_INTERVAL_MS = 24 * 60 * 60 * 1000; // 24 hours
    for (;;) {
        if (wifiConnected) {
            log_message(LOG_LEVEL_INFO, "ColorDB", "Checking for color database refresh...");
            if (!fetchColorDatabase()) {
                log_message(LOG_LEVEL_WARN, "ColorDB", "Failed to refresh color database");
            }
        }
        vTaskDelay(pdMS_TO_TICKS(REFRESH_INTERVAL_MS));
    }
}
// ------------------------------------------------------------------------------------
// Section 7.5: Sensor Health Check
// ------------------------------------------------------------------------------------
bool check_sensor_health() {
    uint8_t status = tcs3430_sensor.getDeviceStatus();
    uint16_t x_val = tcs3430_sensor.getXData();
    uint16_t y_val = tcs3430_sensor.getYData();
    uint16_t z_val = tcs3430_sensor.getZData();
    if (!(status & 0x03)) {
        uint8_t tcs_address = 0x39;
        Wire.beginTransmission(tcs_address);
        Wire.write(0x06); Wire.write(0xE0);
        if (Wire.endTransmission() != 0) {
            log_message(LOG_LEVEL_ERROR, "Health", "Failed to clear interrupts");
        } else {
            Wire.beginTransmission(tcs_address);
            Wire.write(0x00); Wire.write(0x03);
            if (Wire.endTransmission() == 0) {
                vTaskDelay(pdMS_TO_TICKS(100));
                status = tcs3430_sensor.getDeviceStatus();
                log_message(LOG_LEVEL_INFO, "Health", "Re-enabled sensor due to PON=0 or AEN=0");
            } else {
                log_message(LOG_LEVEL_ERROR, "Health", "Failed to re-enable sensor");
            }
        }
    }
    if (status == 0xFF) {
        char status_msg[50];
        snprintf(status_msg, sizeof(status_msg), "Critical sensor failure: 0x%02X", status);
        log_message(LOG_LEVEL_ERROR, "Health", status_msg);
        return false;
    }
    static uint32_t last_health_log = 0;
    if (millis() - last_health_log > 10000) {
        char health_msg[100];
        snprintf(health_msg, sizeof(health_msg), "Sensor Health - Status:0x%02X (PON:%d, AEN:%d, ASAT:%d, AINT:%d), X:%d, Y:%d, Z:%d",
                 status, (status & 0x01), (status & 0x02) >> 1, (status & 0x08) >> 3, (status & 0x10) >> 4, x_val, y_val, z_val);
        log_message(LOG_LEVEL_INFO, "Health", health_msg);
        if (status == 0x00 || (status & 0x10)) {
            char warn_msg[50];
            snprintf(warn_msg, sizeof(warn_msg), "Non-standard status: 0x%02X - likely no object", status);
            log_message(LOG_LEVEL_INFO, "Health", warn_msg);
        }
        last_health_log = millis();
    }
    return true;
}

// ------------------------------------------------------------------------------------
// Section 8: Main Application Logic
// ------------------------------------------------------------------------------------
void IRAM_ATTR handle_sensor_interrupt() { color_change_interrupt_flag = true; }

bool init_sensor() {
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
    Wire.setClock(100000);
    vTaskDelay(pdMS_TO_TICKS(200));
    log_message(LOG_LEVEL_INFO, "Sensor", "Scanning I2C bus for TCS3430...");
    uint8_t tcs_address = 0x39;
    Wire.beginTransmission(tcs_address);
    int error = Wire.endTransmission();
    if (error != 0) {
        char err_msg[50];
        snprintf(err_msg, sizeof(err_msg), "No TCS3430 at 0x%02X. Error: %d", tcs_address, error);
        log_message(LOG_LEVEL_ERROR, "Sensor", err_msg);
        return false;
    }
    Wire.beginTransmission(tcs_address);
    Wire.write(0x18);
    Wire.endTransmission();
    Wire.requestFrom(tcs_address, (uint8_t)1);
    if (Wire.available()) {
        uint8_t device_id = Wire.read();
        if (device_id != 0xD0) {
            char id_msg[50];
            snprintf(id_msg, sizeof(id_msg), "Invalid TCS3430 ID: 0x%02X (expected 0xD0)", device_id);
            log_message(LOG_LEVEL_ERROR, "Sensor", id_msg);
            return false;
        }
        char addr_msg[50];
        snprintf(addr_msg, sizeof(addr_msg), "Found TCS3430 at 0x%02X, ID: 0x%02X", tcs_address, device_id);
        log_message(LOG_LEVEL_INFO, "Sensor", addr_msg);
    } else {
        log_message(LOG_LEVEL_ERROR, "Sensor", "Failed to read TCS3430 ID");
        return false;
    }
    log_message(LOG_LEVEL_INFO, "Sensor", "Initializing TCS3430...");
    for (int attempt = 1; attempt <= 3; attempt++) {
        char msg[50];
        snprintf(msg, sizeof(msg), "Attempt %d", attempt);
        log_message(LOG_LEVEL_INFO, "Sensor", msg);
        Wire.beginTransmission(tcs_address);
        Wire.write(0x06); Wire.write(0xE0);
        if (Wire.endTransmission() != 0) {
            log_message(LOG_LEVEL_ERROR, "Sensor", "I2C failed during interrupt clear");
            vTaskDelay(pdMS_TO_TICKS(500));
            continue;
        }
        Wire.beginTransmission(tcs_address);
        Wire.write(0x00); Wire.write(0x01);
        if (Wire.endTransmission() != 0) {
            log_message(LOG_LEVEL_ERROR, "Sensor", "I2C failed during power-on");
            vTaskDelay(pdMS_TO_TICKS(500));
            continue;
        }
        vTaskDelay(pdMS_TO_TICKS(10));
        Wire.beginTransmission(tcs_address);
        Wire.write(0x00); Wire.write(0x03);
        if (Wire.endTransmission() != 0) {
            log_message(LOG_LEVEL_ERROR, "Sensor", "I2C failed during ADC enable");
            vTaskDelay(pdMS_TO_TICKS(500));
            continue;
        }
        vTaskDelay(pdMS_TO_TICKS(100));
        if (tcs3430_sensor.begin()) {
            uint8_t status = tcs3430_sensor.getDeviceStatus();
            char status_msg[100];
            snprintf(status_msg, sizeof(status_msg), "Sensor status: 0x%02X (PON:%d, AEN:%d, ASAT:%d, AINT:%d)",
                     status, (status & 0x01), (status & 0x02) >> 1, (status & 0x08) >> 3, (status & 0x10) >> 4);
            log_message(LOG_LEVEL_INFO, "Sensor", status_msg);
            log_message(LOG_LEVEL_INFO, "Sensor", "TCS3430 initialized successfully.");
            return true;
        }
        snprintf(msg, sizeof(msg), "Attempt %d failed.", attempt);
        log_message(LOG_LEVEL_WARN, "Sensor", msg);
        vTaskDelay(pdMS_TO_TICKS(500));
    }
    log_message(LOG_LEVEL_ERROR, "Sensor", "TCS3430 init failed after 3 attempts.");
    return false;
}

void setup() {
    Serial.begin(115200);
    vTaskDelay(pdMS_TO_TICKS(2000));
    esp_reset_reason_t reason = esp_reset_reason();
    String reset_reason;
    switch (reason) {
        case ESP_RST_POWERON: reset_reason = "Power-on"; break;
        case ESP_RST_SW: reset_reason = "Software reset"; break;
        case ESP_RST_PANIC: reset_reason = "Exception/panic"; break;
        case ESP_RST_INT_WDT: reset_reason = "Interrupt watchdog"; break;
        case ESP_RST_TASK_WDT: reset_reason = "Task watchdog"; break;
        case ESP_RST_WDT: reset_reason = "Other watchdog"; break;
        case ESP_RST_DEEPSLEEP: reset_reason = "Deep sleep"; break;
        case ESP_RST_BROWNOUT: reset_reason = "Brownout"; break;
        case ESP_RST_SDIO: reset_reason = "SDIO"; break;
        default: reset_reason = String("Unknown (") + String(reason) + ")";
    }
    char msg[100];
    snprintf(msg, sizeof(msg), "Booting ESP32PROS3 Color Matcher. Reset reason: %s", reset_reason.c_str());
    log_message(LOG_LEVEL_INFO, "System", msg);
    pinMode(INDICATOR_LED_PIN, OUTPUT);
    pinMode(ILLUMINATION_LED_PIN, OUTPUT);
    digitalWrite(INDICATOR_LED_PIN, LOW);
    digitalWrite(ILLUMINATION_LED_PIN, LOW);
    g_led_state = false;
    g_illum_led_state = false;
    if (!LittleFS.begin(FORMAT_LITTLEFS_IF_FAILED)) {
        log_message(LOG_LEVEL_ERROR, "LittleFS", "Mount failed.");
        if (FORMAT_LITTLEFS_IF_FAILED) {
            log_message(LOG_LEVEL_INFO, "LittleFS", "Formatting...");
            if (LittleFS.format()) {
                log_message(LOG_LEVEL_INFO, "LittleFS", "Formatted successfully. Rebooting...");
                vTaskDelay(pdMS_TO_TICKS(2000));
                ESP.restart();
            } else {
                log_message(LOG_LEVEL_ERROR, "LittleFS", "Format failed.");
            }
        }
    } else {
        log_message(LOG_LEVEL_INFO, "LittleFS", "Mounted successfully.");
    }
    g_web_data = {0};
    g_web_data.data_ready = false;
    strcpy(g_web_data.matched_name, "No Object");
    strcpy(g_web_data.confidence, "N/A");
    esp_err_t nvs_err = nvs_flash_init();
    if (nvs_err == ESP_ERR_NVS_NO_FREE_PAGES || nvs_err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        log_message(LOG_LEVEL_WARN, "NVS", "Initialization failed, erasing...");
        nvs_flash_erase();
        nvs_err = nvs_flash_init();
        if (nvs_err != ESP_OK) {
            log_message(LOG_LEVEL_ERROR, "NVS", "Init failed after erase.");
        }
    }
    bool sensor_ok = false;
    for (int attempt = 1; attempt <= 3; attempt++) {
        if (init_sensor()) {
            sensor_ok = true;
            break;
        }
        char msg[50];
        snprintf(msg, sizeof(msg), "Init attempt %d failed. Retrying...", attempt);
        log_message(LOG_LEVEL_WARN, "Sensor", msg);
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    if (!sensor_ok) {
        log_message(LOG_LEVEL_ERROR, "Sensor", "TCS3430 initialization failed.");
    } else {
        calibration_init();
    }
    if (SENSOR_INTERRUPT_PIN >= 0) {
        pinMode(SENSOR_INTERRUPT_PIN, INPUT_PULLUP);
        attachInterrupt(digitalPinToInterrupt(SENSOR_INTERRUPT_PIN), handle_sensor_interrupt, FALLING);
    }
    vTaskDelay(pdMS_TO_TICKS(1000)); // Delay WiFi init
    xTaskCreatePinnedToCore(TaskNetwork, "Network Task", NETWORK_TASK_STACK_SIZE, nullptr, 2, &networkTaskHandle, CONFIG_ASYNC_TCP_RUNNING_CORE);
    if (wifiConnected) {
        if (fetchColorDatabase()) {
            log_message(LOG_LEVEL_INFO, "Setup", "Online color database loaded successfully");
        } else if (loadCachedColors()) {
            log_message(LOG_LEVEL_INFO, "Setup", "Loaded cached color database");
        } else {
            log_message(LOG_LEVEL_WARN, "Setup", "Failed to load online or cached colors, using built-in database");
            initialize_color_database();
        }
    } else {
        if (loadCachedColors()) {
            log_message(LOG_LEVEL_INFO, "Setup", "Loaded cached color database");
        } else {
            log_message(LOG_LEVEL_WARN, "Setup", "No WiFi connection, using built-in database");
            initialize_color_database();
        }
    }
    xTaskCreatePinnedToCore(TaskColorDBRefresh, "ColorDB Refresh", 4096, nullptr, 1, nullptr, CONFIG_ASYNC_TCP_RUNNING_CORE);
    log_message(LOG_LEVEL_INFO, "System", sensor_ok ? "Setup complete." : "Setup complete (no sensor).");
}

void loop() {
    uint32_t current_time = millis();
    static uint32_t last_health_check = 0;
    static uint32_t zero_reading_count = 0;
    static uint32_t last_zero_log = 0;
    if (current_time - last_health_check > 5000) {
        if (!check_sensor_health()) {
            log_message(LOG_LEVEL_ERROR, "Loop", "Sensor health check failed - attempting recovery");
            if (init_sensor()) {
                calibration_init();
                log_message(LOG_LEVEL_INFO, "Loop", "Sensor recovery successful");
                zero_reading_count = 0;
            }
        }
        last_health_check = current_time;
    }
    if (current_time - last_sensor_read_time >= READING_INTERVAL_MS && g_is_scanning) {
        last_sensor_read_time = current_time;
        xyz_color_s measured_xyz = {(float)tcs3430_sensor.getXData(), (float)tcs3430_sensor.getYData(), (float)tcs3430_sensor.getZData()};
        uint16_t ir1_raw = tcs3430_sensor.getIR1Data();
        uint16_t ir2_raw = tcs3430_sensor.getIR2Data();
        bool data_invalid = false;
        char diagnostic_msg[100];
        if (isnan(measured_xyz.x) || isnan(measured_xyz.y) || isnan(measured_xyz.z)) {
            snprintf(diagnostic_msg, sizeof(diagnostic_msg), "NaN detected - X:%s, Y:%s, Z:%s",
                     isnan(measured_xyz.x) ? "NaN" : "OK", isnan(measured_xyz.y) ? "NaN" : "OK", isnan(measured_xyz.z) ? "NaN" : "OK");
            log_message(LOG_LEVEL_ERROR, "Sensor", diagnostic_msg);
            data_invalid = true;
        }
        if (measured_xyz.x <= 0.0f && measured_xyz.y <= 0.0f && measured_xyz.z <= 0.0f) {
            zero_reading_count++;
            if (millis() - last_zero_log > 10000) {
                snprintf(diagnostic_msg, sizeof(diagnostic_msg), "Zero values (%d times) - X:%.1f, Y:%.1f, Z:%.1f, IR1:%d, IR2:%d",
                         zero_reading_count, measured_xyz.x, measured_xyz.y, measured_xyz.z, ir1_raw, ir2_raw);
                log_message(LOG_LEVEL_INFO, "Sensor", diagnostic_msg);
                uint8_t sensor_status = tcs3430_sensor.getDeviceStatus();
                snprintf(diagnostic_msg, sizeof(diagnostic_msg), "Sensor status: 0x%02X (PON:%d, AEN:%d, ASAT:%d, AINT:%d)",
                         sensor_status, (sensor_status & 0x01), (sensor_status & 0x02) >> 1, (sensor_status & 0x08) >> 3, (sensor_status & 0x10) >> 4);
                log_message(LOG_LEVEL_INFO, "Sensor", diagnostic_msg);
                last_zero_log = millis();
            }
            if (zero_reading_count > 20 && tcs3430_sensor.getDeviceStatus() == 0xFF) {
                log_message(LOG_LEVEL_ERROR, "Sensor", "Too many zero readings - triggering recovery");
                if (init_sensor()) {
                    calibration_init();
                    zero_reading_count = 0;
                }
            }
            strcpy(g_web_data.matched_name, "No Object");
            g_web_data.data_ready = false;
            return;
        } else {
            zero_reading_count = 0;
        }
        if (data_invalid) {
            uint8_t sensor_status = tcs3430_sensor.getDeviceStatus();
            snprintf(diagnostic_msg, sizeof(diagnostic_msg), "Sensor status: 0x%02X (PON:%d, AEN:%d, ASAT:%d, AINT:%d)",
                     sensor_status, (sensor_status & 0x01), (sensor_status & 0x02) >> 1, (sensor_status & 0x08) >> 3, (sensor_status & 0x10) >> 4);
            log_message(LOG_LEVEL_ERROR, "Sensor", diagnostic_msg);
            strcpy(g_web_data.matched_name, "Invalid Data");
            g_web_data.data_ready = false;
            return;
        }
        apply_ir_compensation(measured_xyz, ir1_raw, ir2_raw, current_app_calibration.k_ir_compensation_factor);
        if (measured_xyz.x <= 0.0f || measured_xyz.y <= 0.0f || measured_xyz.z <= 0.0f || isnan(measured_xyz.x) || isnan(measured_xyz.y) || isnan(measured_xyz.z)) {
            log_message(LOG_LEVEL_WARN, "Sensor", "Invalid XYZ data after IR compensation.");
            strcpy(g_web_data.matched_name, "Invalid Data");
            g_web_data.data_ready = false;
            return;
        }
        lab_color_s measured_lab;
        xyz_to_lab(measured_xyz, measured_lab, D65_WHITEPOINT);
        if (isnan(measured_lab.l) || isnan(measured_lab.a) || isnan(measured_lab.b)) {
            log_message(LOG_LEVEL_WARN, "Sensor", "Invalid Lab data.");
            strcpy(g_web_data.matched_name, "Invalid Data");
            g_web_data.data_ready = false;
            return;
        }
        float delta_e_val = -1.0f;
        int matched_idx = find_closest_color_in_db(measured_lab, delta_e_val);
        rgb_color_s output_rgb = xyz_to_srgb_enhanced(measured_xyz, current_app_calibration.srgb_output_norm_factor);
        if (output_rgb.r > 255 || output_rgb.g > 255 || output_rgb.b > 255) {
            log_message(LOG_LEVEL_WARN, "Sensor", "Invalid RGB values, clamping.");
            output_rgb.r = constrain(output_rgb.r, 0, 255);
            output_rgb.g = constrain(output_rgb.g, 0, 255);
            output_rgb.b = constrain(output_rgb.b, 0, 255);
        }
        g_web_data.measured_r = output_rgb.r;
        g_web_data.measured_g = output_rgb.g;
        g_web_data.measured_b = output_rgb.b;
        g_web_data.delta_e = delta_e_val;
        const char* confidence_str = (delta_e_val <= 5.0f) ? "High" : (delta_e_val <= 10.0f) ? "Medium" : "Low";
        strncpy(g_web_data.confidence, confidence_str, sizeof(g_web_data.confidence) - 1);
        g_web_data.confidence[sizeof(g_web_data.confidence) - 1] = '\0';
        if (matched_idx != -1) {
            strncpy(g_web_data.matched_name, color_database[matched_idx].name, MAX_COLOR_NAME_LEN - 1);
            g_web_data.matched_name[MAX_COLOR_NAME_LEN - 1] = '\0';
            g_web_data.matched_r = color_database[matched_idx].rgb_ref.r;
            g_web_data.matched_g = color_database[matched_idx].rgb_ref.g;
            g_web_data.matched_b = color_database[matched_idx].rgb_ref.b;
        } else {
            strcpy(g_web_data.matched_name, "Unknown");
            g_web_data.matched_r = 0;
            g_web_data.matched_g = 0;
            g_web_data.matched_b = 0;
        }
        g_web_data.avg_x = measured_xyz.x;
        g_web_data.avg_y = measured_xyz.y;
        g_web_data.avg_z = measured_xyz.z;
        g_web_data.avg_l = measured_lab.l;
        g_web_data.avg_a = measured_lab.a;
        g_web_data.avg_b = measured_lab.b;
        g_web_data.avg_ir1 = ir1_raw;
        g_web_data.avg_ir2 = ir2_raw;
        g_web_data.data_ready = true;
        if (!wifiConnected && g_is_scanning) {
            snprintf(logBuffer, sizeof(logBuffer), "RGB: (%d, %d, %d), Match: %s, DeltaE: %.2f, Confidence: %s",
                     g_web_data.measured_r, g_web_data.measured_g, g_web_data.measured_b,
                     g_web_data.matched_name, g_web_data.delta_e, g_web_data.confidence);
            log_message(LOG_LEVEL_INFO, "Sensor", logBuffer);
        }
    }
    vTaskDelay(pdMS_TO_TICKS(10));
}