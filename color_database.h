#ifndef COLOR_DATABASE_H
#define COLOR_DATABASE_H

#include "color_conversion.h" // For rgb_color_s, lab_color_s, xyz_color_s

#define MAX_COLOR_NAME_LEN 30 // Increased length for longer names

// Structure to hold reference color data
typedef struct {
  char name[MAX_COLOR_NAME_LEN];
  rgb_color_s rgb_ref;   // Reference sRGB values (0-255)
  xyz_color_s xyz_ref;   // Pre-calculated CIE XYZ values (derived from rgb_ref, D65)
  lab_color_s lab_ref;   // Pre-calculated CIE L*a*b* values (derived from xyz_ref, D65)
} reference_color_t;

// Declare the global color database array
extern reference_color_t dulux_color_database[];
// Declare the size of the database
extern const int DULUX_COLOR_DB_SIZE;

// Initializes the database by calculating XYZ and L*a*b* values from sRGB_ref.
// Should be called once at startup.
void initialize_color_database();

// Finds the closest color in the database to a measured L*a*b* value.
// Returns the index of the closest color in `dulux_color_database`.
// Populates `out_delta_e` with the calculated Delta E (CIE76) value.
// Returns -1 if the database is empty or an error occurs.
int find_closest_color_in_db(const lab_color_s &measured_lab, float &out_delta_e);

#endif // COLOR_DATABASE_H