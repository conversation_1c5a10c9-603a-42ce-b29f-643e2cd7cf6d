#include "color_database.h"
#include "utilities.h" // For logging

// Define the Dulux color database with sRGB reference values.
// XYZ and Lab values will be calculated by initialize_color_database().
reference_color_t dulux_color_database[] = {
  { "Vivid White",  {247, 248, 244}, {0,0,0}, {0,0,0} }, // Dulux Vivid White™
  { "Aubergine",    {79, 42, 68},    {0,0,0}, {0,0,0} }, // Example: DLX1064-1
  { "Pink Chablis", {205, 164, 158}, {0,0,0}, {0,0,0} }, // Example: DLX1064-2
  { "Red Oxide",    {128, 31, 26},   {0,0,0}, {0,0,0} }, // Example darker color
  { "Forest Green", {34, 87, 55},    {0,0,0}, {0,0,0} }, // Example green
  { "Deep Blue",    {25, 45, 105},   {0,0,0}, {0,0,0} }  // Example blue
  // Add more reference colors here
};
// Calculate the size of the database
const int DULUX_COLOR_DB_SIZE = sizeof(dulux_color_database) / sizeof(dulux_color_database[0]);

void initialize_color_database() {
    if (DULUX_COLOR_DB_SIZE == 0) {
        log_warning("ColorDB", "Color database is empty!");
        return;
    }
    log_info("ColorDB", "Initializing L*a*b* values for reference color database...");
    for (int i = 0; i < DULUX_COLOR_DB_SIZE; i++) {
        // Convert reference sRGB to CIE XYZ (using D65 illuminant, Y scaled to 100 for white)
        dulux_color_database[i].xyz_ref = srgb_to_xyz(dulux_color_database[i].rgb_ref);
        
        // Convert reference CIE XYZ to CIE L*a*b* (using D65 white point)
        xyz_to_lab(dulux_color_database[i].xyz_ref, dulux_color_database[i].lab_ref, D65_WHITEPOINT);

        #if CURRENT_LOG_LEVEL >= LOG_LEVEL_DEBUG // Only log if debug level is enabled
        char msg[128];
        sprintf(msg, "Color: %-15s RGB(%3u,%3u,%3u) -> XYZ(%.1f,%.1f,%.1f) -> Lab(L:%.1f a:%.1f b:%.1f)",
           dulux_color_database[i].name,
           dulux_color_database[i].rgb_ref.r, dulux_color_database[i].rgb_ref.g, dulux_color_database[i].rgb_ref.b,
           dulux_color_database[i].xyz_ref.x, dulux_color_database[i].xyz_ref.y, dulux_color_database[i].xyz_ref.z,
           dulux_color_database[i].lab_ref.l, dulux_color_database[i].lab_ref.a, dulux_color_database[i].lab_ref.b);
        log_debug("ColorDB_Init", msg);
        #endif
    }
    log_info("ColorDB", "Reference color database initialization complete.");
}

int find_closest_color_in_db(const lab_color_s &measured_lab, float &out_delta_e) {
    if (DULUX_COLOR_DB_SIZE == 0) {
        out_delta_e = -1.0f; // Indicate error or empty database
        return -1;
    }

    float min_delta_e = 999999.0f; // Initialize with a very large number
    int closest_color_index = 0;   // Default to the first color

    for (int i = 0; i < DULUX_COLOR_DB_SIZE; i++) {
        float current_delta_e = delta_e_cie76(measured_lab, dulux_color_database[i].lab_ref);
        if (current_delta_e < min_delta_e) {
            min_delta_e = current_delta_e;
            closest_color_index = i;
        }
    }
    out_delta_e = min_delta_e;
    return closest_color_index;
}