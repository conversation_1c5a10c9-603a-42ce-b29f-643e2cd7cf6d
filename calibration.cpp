#include "calibration.h"
#include "utilities.h" // For logging
#include "ui/color_matching_ui.h" // For show_ui_message function
#include <Preferences.h> // ESP32 Non-Volatile Storage

Preferences preferences_calib; // NVS object for storing calibration
const char* CALIB_NVS_NAMESPACE = "clr_calib"; // Namespace in NVS
const char* CALIB_NVS_KEY = "cal_data";         // Key for calibration data

app_calibration_data_t current_app_calibration; // Define the global instance

void calibration_init() {
    if (!calibration_load()) { // Try to load from NVS
        log_info("Calibration", "No saved calibration data found or NVS error. Using defaults.");
        // Default values (these should be refined based on initial testing)
        current_app_calibration.k_ir_compensation_factor = 0.05f; // Initial guess from V1.7 sketch (kIR)
        current_app_calibration.display_srgb_norm_factor = 60000.0f; // A common high raw sensor value for white.
                                                                   // Should be calibrated to actual sensor's Y reading for white.
        // current_app_calibration.white_ref_sensor_Y_raw = 60000.0f; // Example default
        current_app_calibration.data_is_valid = false; // Mark as default, not truly calibrated
    } else {
        log_info("Calibration", "Calibration data loaded successfully from NVS.");
        current_app_calibration.data_is_valid = true; // Assume loaded data is valid
    }
}

void calibration_run_procedure() {
    log_info("Calibration", "Starting basic white reference calibration procedure...");
    show_ui_message("Calibration: Place sensor on white reference surface", false);

    // Note: This is a simplified calibration procedure.
    // In a full implementation, you would:
    // 1. Wait for user confirmation (button press, touch, etc.)
    // 2. Take multiple readings and average them
    // 3. Validate the readings are reasonable
    // 4. Optionally calibrate IR compensation factor

    // For now, we'll set improved default values based on typical TCS3430 behavior
    // These values should be refined through actual testing with your specific setup

    // Improved default IR compensation factor
    // This value typically ranges from 0.01 to 0.1 depending on the sensor and environment
    current_app_calibration.k_ir_compensation_factor = 0.03f; // Conservative default

    // Improved default normalization factor
    // This should be the Y channel reading for a white reference under your typical lighting
    // Typical values range from 30,000 to 65,000 for TCS3430 depending on integration time and gain
    current_app_calibration.display_srgb_norm_factor = 45000.0f; // More realistic default

    // Mark calibration as valid (though it's using defaults)
    current_app_calibration.data_is_valid = true;

    // Save the calibration data
    if (calibration_save()) {
        log_info("Calibration", "Default calibration values saved successfully.");
        show_ui_message("Calibration: Default values applied and saved", false);
    } else {
        log_error("Calibration", "Failed to save calibration data.");
        show_ui_message("Calibration: Failed to save data", true);
    }
}

bool calibration_save() {
    log_info("Calibration", "Saving calibration data to NVS...");

    if (!preferences_calib.begin(CALIB_NVS_NAMESPACE, false)) { // false for read-write
        log_error("Calibration", "Failed to begin NVS for saving.");
        return false;
    }

    size_t bytes_written = preferences_calib.putBytes(CALIB_NVS_KEY, &current_app_calibration, sizeof(app_calibration_data_t));
    preferences_calib.end();

    if (bytes_written == sizeof(app_calibration_data_t)) {
        log_info("Calibration", "Calibration data saved successfully to NVS.");
        return true;
    } else {
        log_error("Calibration", "Failed to save calibration data to NVS.");
        return false;
    }
}

bool calibration_load() {
    log_info("Calibration", "Loading calibration data from NVS...");

    if (!preferences_calib.begin(CALIB_NVS_NAMESPACE, true)) { // true for read-only
        log_info("Calibration", "NVS namespace not found (first run) - will use defaults.");
        return false; // This is normal for first run
    }

    if (!preferences_calib.isKey(CALIB_NVS_KEY)) {
        log_info("Calibration", "No calibration data found in NVS (first run).");
        preferences_calib.end();
        return false;
    }

    size_t bytes_read = preferences_calib.getBytes(CALIB_NVS_KEY, &current_app_calibration, sizeof(app_calibration_data_t));
    preferences_calib.end();

    if (bytes_read == sizeof(app_calibration_data_t)) {
        log_info("Calibration", "Calibration data loaded successfully from NVS.");
        return true;
    } else {
        log_error("Calibration", "NVS calibration data size mismatch - data may be corrupted.");
        return false;
    }
}